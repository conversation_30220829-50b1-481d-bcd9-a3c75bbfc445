#include "infrared.h"
#include "tim.h"
#include "User_Debug.h"
#include "lcd.h"
#include "lcd_init.h"



float angle=0;

void Infrared_FindWay(void)
{	
	//白色就亮
	if(IR5==0 &&IR4==0 && IR1==0 && IR3==0 && IR2==1)	
	{
		
	}
	if(IR5==0 &&IR4==0 && IR1==0&&IR3==1 && IR2==0)	
	{
		
	}
	if(IR5==0 &&IR4==0 && IR1==1&&IR3==0 && IR2==0)	
	{
		
	}
	if(IR5==0 &&IR4==1 && IR1==0&&IR3==0 && IR2==0)	
	{
		
	}
	if(IR5==1 &&IR4==0 && IR1==0&&IR3==0 && IR2==0)	
	{
		
	}
	
}


void displayInfrared(void)
{
	LCD_ShowIntNum(0,106,IR5,1,RED,WHITE,16);  
	LCD_ShowIntNum(25,106,IR4,1,RED,WH<PERSON><PERSON>,16);
	LCD_ShowIntNum(50,106,IR1,1,RED,WHITE,16);
	LCD_ShowIntNum(75,106,IR3,1,RED,WHITE,16);
	LCD_ShowIntNum(100,106,IR2,1,RED,WHITE,16);
}

