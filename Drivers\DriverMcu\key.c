#include "key.h"
#include "stdint.h"
#include "lcd.h"
#include "lcd_init.h"
#include "User_Debug.h"
#include "tim.h"

uint8_t KEY_Scan(uint8_t mode)
{	 
	static uint8_t key_up=1;//按键松开标志位
	if(key_up&&(KEY1==0||KEY2==0))
	{
		HAL_Delay(10);//去抖动
		key_up=0;
		if(KEY1==0)return KEY1_PRES;
		else if(KEY2==0)return KEY2_PRES;
	}
	else if(KEY1==1||KEY2==1)key_up=1; 	    
 	return 0;//无按键按下
}

//驱动按键测速,加速，减速
int8_t countV=0;
uint16_t pwm_val=0;//初始值
void driverKeyTestV(void)
{
	switch(KEY_Scan(0))
	{				 
		case KEY1_PRES:
//			usart_printf("KEY1_PRES\r\n");
			countV+=1;
			if(countV>5)
			{
				countV=0;
			}
		break;
		
		
		case KEY2_PRES:
//			usart_printf("KEY2_PRES\r\n");
			countV-=1;
			if(countV<0)
			{
				countV=5;
			}
		break;
		default:break;
	} 
	
	switch(countV)
	{
		case 0:pwm_val=0;break;
		case 1:pwm_val=550;break;
		case 2:pwm_val=660;break;
		case 3:pwm_val=770;break;
		case 4:pwm_val=880;break;
		case 5:pwm_val=990;break;
		default:break;
	}
//	LCD_ShowString(0,34,"mode:",RED,WHITE,16,0);
//	LCD_ShowIntNum(50,34,countV,1,RED,WHITE,16);  
//	LCD_ShowIntNum(65,34,pwm_val,3,RED,WHITE,16); 
}

