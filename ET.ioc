#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_5
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_4
ADC1.ContinuousConvMode=DISABLE
ADC1.DiscontinuousConvMode=ENABLE
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,NbrOfConversionFlag,master,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,NbrOfConversion,DiscontinuousConvMode,ContinuousConvMode
ADC1.NbrOfConversion=2
ADC1.NbrOfConversionFlag=1
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.Rank-1\#ChannelRegularConversion=2
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
CAN1.BS1=CAN_BS1_10TQ
CAN1.BS2=CAN_BS2_3TQ
CAN1.CalculateBaudRate=250000
CAN1.CalculateTimeBit=4000
CAN1.CalculateTimeQuantum=285.7142857142857
CAN1.IPParameters=CalculateTimeQuantum,CalculateTimeBit,CalculateBaudRate,Prescaler,BS1,BS2
CAN1.Prescaler=12
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F407VET6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=CAN1
Mcu.IP10=TIM6
Mcu.IP11=TIM7
Mcu.IP12=USART1
Mcu.IP13=USART2
Mcu.IP14=USART6
Mcu.IP2=NVIC
Mcu.IP3=RCC
Mcu.IP4=SPI2
Mcu.IP5=SYS
Mcu.IP6=TIM1
Mcu.IP7=TIM2
Mcu.IP8=TIM3
Mcu.IP9=TIM4
Mcu.IPNb=15
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PA3
Mcu.Pin11=PA4
Mcu.Pin12=PA5
Mcu.Pin13=PA6
Mcu.Pin14=PA7
Mcu.Pin15=PC5
Mcu.Pin16=PB1
Mcu.Pin17=PE9
Mcu.Pin18=PE11
Mcu.Pin19=PB12
Mcu.Pin2=PE4
Mcu.Pin20=PB13
Mcu.Pin21=PB14
Mcu.Pin22=PB15
Mcu.Pin23=PC6
Mcu.Pin24=PC7
Mcu.Pin25=PA8
Mcu.Pin26=PA9
Mcu.Pin27=PA10
Mcu.Pin28=PA13
Mcu.Pin29=PA14
Mcu.Pin3=PC14-OSC32_IN
Mcu.Pin30=PD0
Mcu.Pin31=PD1
Mcu.Pin32=PD3
Mcu.Pin33=PD4
Mcu.Pin34=PB6
Mcu.Pin35=PB7
Mcu.Pin36=PE0
Mcu.Pin37=PE1
Mcu.Pin38=VP_SYS_VS_Systick
Mcu.Pin39=VP_TIM1_VS_ClockSourceINT
Mcu.Pin4=PC15-OSC32_OUT
Mcu.Pin40=VP_TIM2_VS_ClockSourceINT
Mcu.Pin41=VP_TIM6_VS_ClockSourceINT
Mcu.Pin42=VP_TIM7_VS_ClockSourceINT
Mcu.Pin5=PH0-OSC_IN
Mcu.Pin6=PH1-OSC_OUT
Mcu.Pin7=PA0-WKUP
Mcu.Pin8=PA1
Mcu.Pin9=PA2
Mcu.PinsNb=43
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.12.0
MxDb.Version=DB.6.0.120
NVIC.ADC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.CAN1_RX0_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.CAN1_RX1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.CAN1_SCE_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.CAN1_TX_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.EXTI4_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM6_DAC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.TIM7_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.Locked=true
PA0-WKUP.Signal=GPIO_Input
PA1.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PA1.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PA1.GPIO_PuPd=GPIO_NOPULL
PA1.Locked=true
PA1.PinState=GPIO_PIN_SET
PA1.Signal=GPIO_Output
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA4.Locked=true
PA4.Signal=ADCx_IN4
PA5.Locked=true
PA5.Signal=ADCx_IN5
PA6.Signal=S_TIM3_CH1
PA7.Signal=S_TIM3_CH2
PA8.Locked=true
PA8.Signal=GPIO_Output
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB1.GPIOParameters=GPIO_Label
PB1.GPIO_Label=BLK
PB1.Locked=true
PB1.Signal=GPIO_Output
PB12.GPIOParameters=GPIO_Label
PB12.GPIO_Label=CS
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.Locked=true
PB13.Mode=Full_Duplex_Master
PB13.Signal=SPI2_SCK
PB14.Locked=true
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.Locked=true
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB6.Locked=true
PB6.Signal=S_TIM4_CH1
PB7.Locked=true
PB7.Signal=S_TIM4_CH2
PC14-OSC32_IN.Mode=LSE-External-Clock-Source
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Clock-Source
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC5.GPIOParameters=GPIO_Label
PC5.GPIO_Label=DC
PC5.Locked=true
PC5.Signal=GPIO_Output
PC6.Mode=Asynchronous
PC6.Signal=USART6_TX
PC7.Mode=Asynchronous
PC7.Signal=USART6_RX
PD0.Locked=true
PD0.Mode=CAN_Activate
PD0.Signal=CAN1_RX
PD1.Mode=CAN_Activate
PD1.Signal=CAN1_TX
PD3.GPIOParameters=GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD3.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD3.GPIO_PuPd=GPIO_NOPULL
PD3.Locked=true
PD3.Signal=GPIO_Output
PD4.GPIOParameters=GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD4.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD4.GPIO_PuPd=GPIO_NOPULL
PD4.Locked=true
PD4.Signal=GPIO_Output
PE0.GPIOParameters=GPIO_Mode
PE0.GPIO_Mode=GPIO_MODE_INPUT
PE0.Locked=true
PE0.Signal=GPIO_Input
PE1.GPIOParameters=GPIO_Mode
PE1.GPIO_Mode=GPIO_MODE_INPUT
PE1.Locked=true
PE1.Signal=GPIO_Input
PE11.Locked=true
PE11.Signal=S_TIM1_CH2
PE2.GPIOParameters=GPIO_Label
PE2.GPIO_Label=data
PE2.Locked=true
PE2.Signal=GPIO_Output
PE3.GPIOParameters=GPIO_Label
PE3.GPIO_Label=trig
PE3.Locked=true
PE3.Signal=GPIO_Output
PE4.GPIOParameters=GPIO_Label,GPIO_ModeDefaultEXTI
PE4.GPIO_Label=echo
PE4.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PE4.Locked=true
PE4.Signal=GPXTI4
PE9.Locked=true
PE9.Signal=S_TIM1_CH1
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=ET.ioc
ProjectManager.ProjectName=ET
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_SPI2_Init-SPI2-false-HAL-true,4-MX_USART2_UART_Init-USART2-false-HAL-true,5-MX_TIM7_Init-TIM7-false-HAL-true,6-MX_TIM1_Init-TIM1-false-HAL-true,7-MX_TIM4_Init-TIM4-false-HAL-true,8-MX_TIM3_Init-TIM3-false-HAL-true,9-MX_TIM6_Init-TIM6-false-HAL-true,10-MX_ADC1_Init-ADC1-false-HAL-true,11-MX_TIM2_Init-TIM2-false-HAL-true,12-MX_CAN1_Init-CAN1-false-HAL-true,13-MX_USART1_UART_Init-USART1-false-HAL-true,14-MX_USART6_UART_Init-USART6-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=8
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.ADCx_IN4.0=ADC1_IN4,IN4
SH.ADCx_IN4.ConfNb=1
SH.ADCx_IN5.0=ADC1_IN5,IN5
SH.ADCx_IN5.ConfNb=1
SH.GPXTI4.0=GPIO_EXTI4
SH.GPXTI4.ConfNb=1
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,PWM Generation2 CH2
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,Encoder_Interface
SH.S_TIM4_CH1.ConfNb=1
SH.S_TIM4_CH2.0=TIM4_CH2,Encoder_Interface
SH.S_TIM4_CH2.ConfNb=1
SPI2.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_2
SPI2.CalculateBaudRate=21.0 MBits/s
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
TIM1.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM1.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM1.IPParameters=Channel-PWM Generation1 CH1,Prescaler,Period,Pulse-PWM Generation1 CH1,Channel-PWM Generation2 CH2,Pulse-PWM Generation2 CH2
TIM1.Period=1000-1
TIM1.Prescaler=72-1
TIM1.Pulse-PWM\ Generation1\ CH1=500-1
TIM1.Pulse-PWM\ Generation2\ CH2=500-1
TIM2.IPParameters=Prescaler,Period
TIM2.Period=65535
TIM2.Prescaler=72-1
TIM6.IPParameters=Prescaler,Period
TIM6.Period=10000-1
TIM6.Prescaler=7200-1
TIM7.IPParameters=Prescaler,Period
TIM7.Period=10000-1
TIM7.Prescaler=7200-1
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART6.IPParameters=VirtualMode
USART6.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
VP_TIM7_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM7_VS_ClockSourceINT.Signal=TIM7_VS_ClockSourceINT
board=custom
