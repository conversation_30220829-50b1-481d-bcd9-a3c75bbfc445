#ifndef INFRARED_H
#define INFRARED_H

#include "usart.h"
#include "stdio.h"
#include "string.h"
#include "main.h"

#define IR1 HAL_GPIO_ReadPin(GPIOD, GPIO_PIN_11)
#define IR2 HAL_GPIO_ReadPin(GPIOD, GPIO_PIN_12)
#define IR3 HAL_GPIO_ReadPin(GPIOD, GPIO_PIN_13)
#define IR4 HAL_GPIO_ReadPin(GPIOD, GPIO_PIN_14)
#define IR5 HAL_GPIO_ReadPin(GPIOD, GPIO_PIN_15)

void Infrared_FindWay(void);
void displayInfrared(void);
#endif




