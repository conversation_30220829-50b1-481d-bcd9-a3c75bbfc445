--cpu Cortex-M4.fp
"et\startup_stm32f407xx.o"
"et\main.o"
"et\gpio.o"
"et\adc.o"
"et\can.o"
"et\spi.o"
"et\tim.o"
"et\usart.o"
"et\stm32f4xx_it.o"
"et\stm32f4xx_hal_msp.o"
"et\pid.o"
"et\stm32f4xx_hal_adc.o"
"et\stm32f4xx_hal_adc_ex.o"
"et\stm32f4xx_ll_adc.o"
"et\stm32f4xx_hal_rcc.o"
"et\stm32f4xx_hal_rcc_ex.o"
"et\stm32f4xx_hal_flash.o"
"et\stm32f4xx_hal_flash_ex.o"
"et\stm32f4xx_hal_flash_ramfunc.o"
"et\stm32f4xx_hal_gpio.o"
"et\stm32f4xx_hal_dma_ex.o"
"et\stm32f4xx_hal_dma.o"
"et\stm32f4xx_hal_pwr.o"
"et\stm32f4xx_hal_pwr_ex.o"
"et\stm32f4xx_hal_cortex.o"
"et\stm32f4xx_hal.o"
"et\stm32f4xx_hal_exti.o"
"et\stm32f4xx_hal_can.o"
"et\stm32f4xx_hal_spi.o"
"et\stm32f4xx_hal_tim.o"
"et\stm32f4xx_hal_tim_ex.o"
"et\stm32f4xx_hal_uart.o"
"et\system_stm32f4xx.o"
"et\infrared.o"
"et\key.o"
"et\lcd.o"
"et\lcd_init.o"
"et\user_debug.o"
"et\dht11.o"
--strict --scatter "ET\ET.sct"
--summary_stderr --info summarysizes --map --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "ET.map" -o ET\ET.axf