Component: ARM Compiler 5.05 update 1 (build 106) Tool: armlink [4d0efa]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.EXTI4_IRQHandler) for EXTI4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.ADC_IRQHandler) for ADC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.CAN1_TX_IRQHandler) for CAN1_TX_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.CAN1_RX0_IRQHandler) for CAN1_RX0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.CAN1_RX1_IRQHandler) for CAN1_RX1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.CAN1_SCE_IRQHandler) for CAN1_SCE_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM7_IRQHandler) for TIM7_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.HAL_ADC_ConvCpltCallback) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    main.o(i.HAL_ADC_ConvCpltCallback) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.HAL_ADC_ConvCpltCallback) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(i.HAL_ADC_ConvCpltCallback) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(i.HAL_ADC_ConvCpltCallback) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    main.o(i.HAL_ADC_ConvCpltCallback) refers to adc.o(.bss) for hadc1
    main.o(i.HAL_ADC_ConvCpltCallback) refers to main.o(.data) for .data
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to __2sprintf.o(.text) for __2sprintf
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to infrared.o(i.displayInfrared) for displayInfrared
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to tim.o(.bss) for htim6
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to main.o(.data) for .data
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to main.o(.bss) for .bss
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.get_pwm_from_speed) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    main.o(i.get_pwm_from_speed) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(i.get_pwm_from_speed) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    main.o(i.get_pwm_from_speed) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to spi.o(i.MX_SPI2_Init) for MX_SPI2_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM7_Init) for MX_TIM7_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM6_Init) for MX_TIM6_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to can.o(i.MX_CAN1_Init) for MX_CAN1_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART6_UART_Init) for MX_USART6_UART_Init
    main.o(i.main) refers to lcd_init.o(i.LCD_Init) for LCD_Init
    main.o(i.main) refers to lcd.o(i.LCD_Fill) for LCD_Fill
    main.o(i.main) refers to can.o(i.CAN1_Mode_Init) for CAN1_Mode_Init
    main.o(i.main) refers to can.o(i.CAN_Config) for CAN_Config
    main.o(i.main) refers to user_debug.o(i.usart_printf) for usart_printf
    main.o(i.main) refers to user_debug.o(i.usart_printfESP8266) for usart_printfESP8266
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) for HAL_TIM_Encoder_Start_IT
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    main.o(i.main) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.main) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) for HAL_ADC_Start_IT
    main.o(i.main) refers to pid.o(i.PID_Init) for PID_Init
    main.o(i.main) refers to infrared.o(i.displayInfrared) for displayInfrared
    main.o(i.main) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    main.o(i.main) refers to dht11.o(i.DH11_ReadDate_Handle) for DH11_ReadDate_Handle
    main.o(i.main) refers to lcd.o(i.LCD_ShowChinese) for LCD_ShowChinese
    main.o(i.main) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    main.o(i.main) refers to lcd.o(i.LCD_ShowIntNum) for LCD_ShowIntNum
    main.o(i.main) refers to lcd.o(i.LCD_ShowFloatNum1) for LCD_ShowFloatNum1
    main.o(i.main) refers to lcd.o(i.LCDDisplayData) for LCDDisplayData
    main.o(i.main) refers to key.o(i.driverKeyTestV) for driverKeyTestV
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to can.o(i.CAN_senddata) for CAN_senddata
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to usart.o(.bss) for huart1
    main.o(i.main) refers to tim.o(.bss) for htim4
    main.o(i.main) refers to adc.o(.bss) for hadc1
    main.o(i.main) refers to dht11.o(.data) for tem_integer
    main.o(i.main) refers to key.o(.data) for pwm_val
    main.o(i.main) refers to can.o(.data) for TXmessage
    main.o(i.main) refers to can.o(.data) for CAN_ID
    main.o(i.main) refers to can.o(.bss) for hcan1
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    can.o(i.CAN1_Mode_Init) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    can.o(i.CAN1_Mode_Init) refers to stm32f4xx_hal_can.o(i.HAL_CAN_Init) for HAL_CAN_Init
    can.o(i.CAN1_Mode_Init) refers to can.o(.bss) for .bss
    can.o(i.CAN1_Receive_Msg) refers to stm32f4xx_hal_can.o(i.HAL_CAN_GetRxFifoFillLevel) for HAL_CAN_GetRxFifoFillLevel
    can.o(i.CAN1_Receive_Msg) refers to stm32f4xx_hal_can.o(i.HAL_CAN_GetRxMessage) for HAL_CAN_GetRxMessage
    can.o(i.CAN1_Receive_Msg) refers to can.o(.bss) for .bss
    can.o(i.CAN_Config) refers to stm32f4xx_hal_can.o(i.HAL_CAN_ConfigFilter) for HAL_CAN_ConfigFilter
    can.o(i.CAN_Config) refers to main.o(i.Error_Handler) for Error_Handler
    can.o(i.CAN_Config) refers to stm32f4xx_hal_can.o(i.HAL_CAN_Start) for HAL_CAN_Start
    can.o(i.CAN_Config) refers to stm32f4xx_hal_can.o(i.HAL_CAN_ActivateNotification) for HAL_CAN_ActivateNotification
    can.o(i.CAN_Config) refers to can.o(.bss) for .bss
    can.o(i.CAN_senddata) refers to stm32f4xx_hal_can.o(i.HAL_CAN_AddTxMessage) for HAL_CAN_AddTxMessage
    can.o(i.CAN_senddata) refers to can.o(.bss) for .bss
    can.o(i.CAN_senddata) refers to can.o(.data) for .data
    can.o(i.HAL_CAN_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    can.o(i.HAL_CAN_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    can.o(i.HAL_CAN_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    can.o(i.HAL_CAN_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    can.o(i.HAL_CAN_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    can.o(i.HAL_CAN_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    can.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to stm32f4xx_hal_can.o(i.HAL_CAN_GetRxMessage) for HAL_CAN_GetRxMessage
    can.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to can.o(.data) for .data
    can.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to can.o(.bss) for .bss
    can.o(i.MX_CAN1_Init) refers to stm32f4xx_hal_can.o(i.HAL_CAN_Init) for HAL_CAN_Init
    can.o(i.MX_CAN1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    can.o(i.MX_CAN1_Init) refers to can.o(.bss) for .bss
    spi.o(i.HAL_SPI_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(i.HAL_SPI_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    spi.o(i.HAL_SPI_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.MX_SPI2_Init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI2_Init) refers to spi.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM6_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM6_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM7_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM7_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM7_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM7_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART6_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART6_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) for HAL_ADC_IRQHandler
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to adc.o(.bss) for hadc1
    stm32f4xx_it.o(i.CAN1_RX0_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) for HAL_CAN_IRQHandler
    stm32f4xx_it.o(i.CAN1_RX0_IRQHandler) refers to can.o(.bss) for hcan1
    stm32f4xx_it.o(i.CAN1_RX1_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) for HAL_CAN_IRQHandler
    stm32f4xx_it.o(i.CAN1_RX1_IRQHandler) refers to can.o(.bss) for hcan1
    stm32f4xx_it.o(i.CAN1_SCE_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) for HAL_CAN_IRQHandler
    stm32f4xx_it.o(i.CAN1_SCE_IRQHandler) refers to can.o(.bss) for hcan1
    stm32f4xx_it.o(i.CAN1_TX_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) for HAL_CAN_IRQHandler
    stm32f4xx_it.o(i.CAN1_TX_IRQHandler) refers to can.o(.bss) for hcan1
    stm32f4xx_it.o(i.EXTI4_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) refers to tim.o(.bss) for htim6
    stm32f4xx_it.o(i.TIM7_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM7_IRQHandler) refers to tim.o(.bss) for htim7
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to main.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to main.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to main.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_can.o(i.HAL_CAN_DeInit) refers to stm32f4xx_hal_can.o(i.HAL_CAN_Stop) for HAL_CAN_Stop
    stm32f4xx_hal_can.o(i.HAL_CAN_DeInit) refers to can.o(i.HAL_CAN_MspDeInit) for HAL_CAN_MspDeInit
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback) for HAL_CAN_TxMailbox0CompleteCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback) for HAL_CAN_TxMailbox0AbortCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback) for HAL_CAN_TxMailbox1CompleteCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback) for HAL_CAN_TxMailbox1AbortCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback) for HAL_CAN_TxMailbox2CompleteCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback) for HAL_CAN_TxMailbox2AbortCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback) for HAL_CAN_RxFifo0FullCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to can.o(i.HAL_CAN_RxFifo0MsgPendingCallback) for HAL_CAN_RxFifo0MsgPendingCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback) for HAL_CAN_RxFifo1FullCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback) for HAL_CAN_RxFifo1MsgPendingCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_SleepCallback) for HAL_CAN_SleepCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback) for HAL_CAN_WakeUpFromRxMsgCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f4xx_hal_can.o(i.HAL_CAN_ErrorCallback) for HAL_CAN_ErrorCallback
    stm32f4xx_hal_can.o(i.HAL_CAN_Init) refers to can.o(i.HAL_CAN_MspInit) for HAL_CAN_MspInit
    stm32f4xx_hal_can.o(i.HAL_CAN_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_can.o(i.HAL_CAN_Start) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_can.o(i.HAL_CAN_Stop) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    infrared.o(i.Infrared_FindWay) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    infrared.o(i.displayInfrared) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    infrared.o(i.displayInfrared) refers to lcd.o(i.LCD_ShowIntNum) for LCD_ShowIntNum
    key.o(i.KEY_Scan) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.KEY_Scan) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    key.o(i.KEY_Scan) refers to key.o(.data) for .data
    key.o(i.driverKeyTestV) refers to key.o(i.KEY_Scan) for KEY_Scan
    key.o(i.driverKeyTestV) refers to key.o(.data) for .data
    lcd.o(i.Draw_Circle) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCDDisplayData) refers to lcd.o(i.LCD_ShowFloatNum1) for LCD_ShowFloatNum1
    lcd.o(i.LCDDisplayData) refers to lcd.o(i.LCD_ShowChinese) for LCD_ShowChinese
    lcd.o(i.LCDDisplayData) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCDDisplayData) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    lcd.o(i.LCDDisplayData) refers to lcd.o(i.LCD_ShowIntNum) for LCD_ShowIntNum
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_DrawPoint) refers to lcd_init.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_DrawPoint) refers to lcd_init.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_DrawRectangle) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    lcd.o(i.LCD_Fill) refers to lcd_init.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_Fill) refers to lcd_init.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_ShowChar) refers to lcd_init.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_ShowChar) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_ShowChar) refers to lcd_init.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.constdata) for .constdata
    lcd.o(i.LCD_ShowChinese) refers to lcd.o(i.LCD_ShowChinese32x32) for LCD_ShowChinese32x32
    lcd.o(i.LCD_ShowChinese) refers to lcd.o(i.LCD_ShowChinese16x16) for LCD_ShowChinese16x16
    lcd.o(i.LCD_ShowChinese) refers to lcd.o(i.LCD_ShowChinese24x24) for LCD_ShowChinese24x24
    lcd.o(i.LCD_ShowChinese16x16) refers to lcd_init.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_ShowChinese16x16) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_ShowChinese16x16) refers to lcd_init.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_ShowChinese16x16) refers to lcd.o(.constdata) for .constdata
    lcd.o(i.LCD_ShowChinese24x24) refers to lcd_init.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_ShowChinese24x24) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_ShowChinese24x24) refers to lcd_init.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_ShowChinese24x24) refers to lcd.o(.constdata) for .constdata
    lcd.o(i.LCD_ShowChinese32x32) refers to lcd_init.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_ShowChinese32x32) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_ShowChinese32x32) refers to lcd_init.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_ShowChinese32x32) refers to lcd.o(.constdata) for .constdata
    lcd.o(i.LCD_ShowFloatNum1) refers to lcd.o(i.mypow) for mypow
    lcd.o(i.LCD_ShowFloatNum1) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowIntNum) refers to lcd.o(i.mypow) for mypow
    lcd.o(i.LCD_ShowIntNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowPicture) refers to lcd_init.o(i.LCD_Address_Set) for LCD_Address_Set
    lcd.o(i.LCD_ShowPicture) refers to lcd_init.o(i.LCD_WR_DATA8) for LCD_WR_DATA8
    lcd.o(i.LCD_ShowString) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd_init.o(i.LCD_Address_Set) refers to lcd_init.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd_init.o(i.LCD_Address_Set) refers to lcd_init.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd_init.o(i.LCD_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd_init.o(i.LCD_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    lcd_init.o(i.LCD_Init) refers to lcd_init.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd_init.o(i.LCD_Init) refers to lcd_init.o(i.LCD_WR_DATA8) for LCD_WR_DATA8
    lcd_init.o(i.LCD_WR_DATA) refers to lcd_init.o(i.LCD_Writ_Bus) for LCD_Writ_Bus
    lcd_init.o(i.LCD_WR_DATA8) refers to lcd_init.o(i.LCD_Writ_Bus) for LCD_Writ_Bus
    lcd_init.o(i.LCD_WR_REG) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd_init.o(i.LCD_WR_REG) refers to lcd_init.o(i.LCD_Writ_Bus) for LCD_Writ_Bus
    lcd_init.o(i.LCD_Writ_Bus) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd_init.o(i.LCD_Writ_Bus) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    lcd_init.o(i.LCD_Writ_Bus) refers to spi.o(.bss) for hspi2
    user_debug.o(i.usart_printf) refers to vsprintf.o(.text) for vsprintf
    user_debug.o(i.usart_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    user_debug.o(i.usart_printf) refers to user_debug.o(.data) for .data
    user_debug.o(i.usart_printf) refers to user_debug.o(.bss) for .bss
    user_debug.o(i.usart_printf) refers to usart.o(.bss) for huart2
    user_debug.o(i.usart_printfESP8266) refers to vsprintf.o(.text) for vsprintf
    user_debug.o(i.usart_printfESP8266) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    user_debug.o(i.usart_printfESP8266) refers to user_debug.o(.data) for .data
    user_debug.o(i.usart_printfESP8266) refers to user_debug.o(.bss) for .bss
    user_debug.o(i.usart_printfESP8266) refers to usart.o(.bss) for huart1
    user_debug.o(i.usart_printftest) refers to vsprintf.o(.text) for vsprintf
    user_debug.o(i.usart_printftest) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    user_debug.o(i.usart_printftest) refers to user_debug.o(.data) for .data
    user_debug.o(i.usart_printftest) refers to user_debug.o(.bss) for .bss
    user_debug.o(i.usart_printftest) refers to usart.o(.bss) for huart6
    dht11.o(i.DH11_ReadDate_Handle) refers to dht11.o(i.DHT11_SendOut) for DHT11_SendOut
    dht11.o(i.DH11_ReadDate_Handle) refers to dht11.o(i.DHT11_Check) for DHT11_Check
    dht11.o(i.DH11_ReadDate_Handle) refers to dht11.o(i.DHT11_ReadByte) for DHT11_ReadByte
    dht11.o(i.DH11_ReadDate_Handle) refers to dht11.o(.data) for .data
    dht11.o(i.DHT11_Check) refers to dht11.o(i.DHT11_IO_IN) for DHT11_IO_IN
    dht11.o(i.DHT11_Check) refers to dht11.o(i.delay_us) for delay_us
    dht11.o(i.DHT11_Check) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    dht11.o(i.DHT11_IO_IN) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dht11.o(i.DHT11_IO_OUT) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dht11.o(i.DHT11_ReadBit) refers to dht11.o(i.delay_us) for delay_us
    dht11.o(i.DHT11_ReadBit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    dht11.o(i.DHT11_ReadByte) refers to dht11.o(i.DHT11_ReadBit) for DHT11_ReadBit
    dht11.o(i.DHT11_SendOut) refers to dht11.o(i.DHT11_IO_OUT) for DHT11_IO_OUT
    dht11.o(i.DHT11_SendOut) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    dht11.o(i.DHT11_SendOut) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    dht11.o(i.DHT11_SendOut) refers to dht11.o(i.delay_us) for delay_us
    dht11.o(i.delay_us) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    dht11.o(i.delay_us) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    dht11.o(i.delay_us) refers to tim.o(.bss) for htim2
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.get_pwm_from_speed), (264 bytes).
    Removing main.o(.bss), (50 bytes).
    Removing main.o(.bss), (36 bytes).
    Removing main.o(.bss), (400 bytes).
    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (50 bytes).
    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (4 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (52 bytes).
    Removing can.o(.rev16_text), (4 bytes).
    Removing can.o(.revsh_text), (4 bytes).
    Removing can.o(.rrx_text), (6 bytes).
    Removing can.o(i.CAN1_Receive_Msg), (72 bytes).
    Removing can.o(i.HAL_CAN_MspDeInit), (72 bytes).
    Removing can.o(.data), (4 bytes).
    Removing can.o(.data), (4 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.HAL_SPI_MspDeInit), (44 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (96 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (72 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (120 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing pid.o(i.PID_Calc), (74 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt), (110 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAError), (22 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (10 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (116 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (62 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (170 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (124 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start), (276 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA), (364 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (108 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (90 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (468 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (62 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (144 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (240 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (248 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (86 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (94 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (84 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (276 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (96 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (60 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (144 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (328 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (324 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (208 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (88 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (24 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (48 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (160 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (112 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (316 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (36 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (32 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2824 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (44 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam), (120 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (102 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (464 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Init), (212 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (286 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (84 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT), (110 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (100 bytes).
    Removing stm32f4xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (86 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (112 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (184 bytes).
    Removing stm32f4xx_hal_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_can.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_AbortTxRequest), (70 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_DeInit), (44 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_DeactivateNotification), (36 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_GetError), (4 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_GetRxFifoFillLevel), (38 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_GetState), (36 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_GetTxMailboxesFreeLevel), (44 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_GetTxTimestamp), (40 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_IsSleepActive), (28 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_IsTxMessagePending), (30 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_MspInit), (2 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_RequestSleep), (38 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_ResetError), (34 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo0MsgPendingCallback), (2 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_Stop), (98 bytes).
    Removing stm32f4xx_hal_can.o(i.HAL_CAN_WakeUp), (84 bytes).
    Removing stm32f4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort), (296 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT), (304 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop), (68 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (260 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive), (346 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (176 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (506 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (296 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (160 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (208 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (148 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (48 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR), (88 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR), (28 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (152 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR), (76 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR), (128 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError), (16 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (108 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback), (102 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt), (100 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (90 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback), (116 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction), (140 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT), (32 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (72 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (212 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (234 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (452 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (452 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (168 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (496 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (170 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (234 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (210 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (36 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (72 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (74 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (272 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (516 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (340 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (114 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (208 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (72 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (74 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (492 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (288 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (258 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (72 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (492 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (288 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (80 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (80 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig), (56 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (36 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (64 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (210 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (172 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (240 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (184 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (212 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (436 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (272 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (212 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (436 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (272 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (32 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (58 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (248 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (86 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (86 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (216 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (168 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (116 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (248 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (52 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (12 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (178 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (124 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (148 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (136 bytes).
    Removing infrared.o(.rev16_text), (4 bytes).
    Removing infrared.o(.revsh_text), (4 bytes).
    Removing infrared.o(.rrx_text), (6 bytes).
    Removing infrared.o(i.Infrared_FindWay), (288 bytes).
    Removing infrared.o(.data), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rrx_text), (6 bytes).
    Removing lcd.o(i.Draw_Circle), (188 bytes).
    Removing lcd.o(i.LCD_DrawLine), (154 bytes).
    Removing lcd.o(i.LCD_DrawRectangle), (74 bytes).
    Removing lcd.o(i.LCD_ShowPicture), (82 bytes).
    Removing lcd_init.o(.rev16_text), (4 bytes).
    Removing lcd_init.o(.revsh_text), (4 bytes).
    Removing lcd_init.o(.rrx_text), (6 bytes).
    Removing user_debug.o(.rev16_text), (4 bytes).
    Removing user_debug.o(.revsh_text), (4 bytes).
    Removing user_debug.o(.rrx_text), (6 bytes).
    Removing user_debug.o(i.usart_printftest), (60 bytes).
    Removing dht11.o(.rev16_text), (4 bytes).
    Removing dht11.o(.revsh_text), (4 bytes).
    Removing dht11.o(.rrx_text), (6 bytes).

559 unused section(s) (total 40968 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/can.c                        0x00000000   Number         0  can.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_can.c 0x00000000   Number         0  stm32f4xx_hal_can.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c 0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\can.c                        0x00000000   Number         0  can.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\DriverMcu\User_Debug.c        0x00000000   Number         0  user_debug.o ABSOLUTE
    ..\Drivers\DriverMcu\dht11.c             0x00000000   Number         0  dht11.o ABSOLUTE
    ..\Drivers\DriverMcu\infrared.c          0x00000000   Number         0  infrared.o ABSOLUTE
    ..\Drivers\DriverMcu\key.c               0x00000000   Number         0  key.o ABSOLUTE
    ..\Drivers\DriverMcu\lcd.c               0x00000000   Number         0  lcd.o ABSOLUTE
    ..\Drivers\DriverMcu\lcd_init.c          0x00000000   Number         0  lcd_init.o ABSOLUTE
    ..\Drivers\DriverMcu\pid.c               0x00000000   Number         0  pid.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_can.c 0x00000000   Number         0  stm32f4xx_hal_can.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\Drivers\\DriverMcu\\User_Debug.c     0x00000000   Number         0  user_debug.o ABSOLUTE
    ..\\Drivers\\DriverMcu\\dht11.c          0x00000000   Number         0  dht11.o ABSOLUTE
    ..\\Drivers\\DriverMcu\\infrared.c       0x00000000   Number         0  infrared.o ABSOLUTE
    ..\\Drivers\\DriverMcu\\key.c            0x00000000   Number         0  key.o ABSOLUTE
    ..\\Drivers\\DriverMcu\\lcd.c            0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\Drivers\\DriverMcu\\lcd_init.c       0x00000000   Number         0  lcd_init.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x080001fc   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000202   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000208   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800020e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000214   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800021a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000220   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800022a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000230   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000236   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800023c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000242   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000248   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800024e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000254   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800025a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x08000260   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x08000266   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000270   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000276   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x0800027c   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x08000282   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x08000288   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800028c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800028e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000292   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000298   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000298   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002a4   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002ae   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002b0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x080002b2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x080002b4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002b4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002b4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002ba   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002ba   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002be   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002be   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002c6   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002c8   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002c8   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002cc   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080002d4   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x080002d4   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000314   Section      238  lludivv7m.o(.text)
    .text                                    0x08000404   Section        0  vsprintf.o(.text)
    .text                                    0x08000428   Section        0  __2sprintf.o(.text)
    .text                                    0x08000454   Section        0  _printf_dec.o(.text)
    .text                                    0x080004cc   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000654   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080006b8   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000706   Section        0  heapauxi.o(.text)
    .text                                    0x0800070c   Section        0  _printf_pad.o(.text)
    .text                                    0x0800075a   Section        0  _printf_truncate.o(.text)
    .text                                    0x0800077e   Section        0  _printf_str.o(.text)
    .text                                    0x080007d0   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000882   Section        0  _printf_charcount.o(.text)
    .text                                    0x080008ac   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x080008ad   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x080008dc   Section        0  _sputc.o(.text)
    .text                                    0x080008e8   Section        0  _printf_wctomb.o(.text)
    .text                                    0x080009a4   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000a20   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08000a21   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000a90   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000a91   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000b24   Section      138  lludiv10.o(.text)
    .text                                    0x08000bae   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000bb1   Thumb Code   428  _printf_fp_dec.o(.text)
    .text                                    0x08000fc8   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x080012c4   Section        0  _printf_char.o(.text)
    .text                                    0x080012f0   Section        0  _printf_wchar.o(.text)
    .text                                    0x0800131c   Section        0  _wcrtomb.o(.text)
    .text                                    0x0800135c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080013a8   Section       16  rt_ctype_table.o(.text)
    .text                                    0x080013b8   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080013c0   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001440   Section        0  bigflt0.o(.text)
    .text                                    0x0800151c   Section        0  exit.o(.text)
    .text                                    0x08001528   Section        8  libspace.o(.text)
    .text                                    0x08001530   Section      128  strcmpv7m.o(.text)
    .text                                    0x080015b0   Section        0  sys_exit.o(.text)
    .text                                    0x080015bc   Section        2  use_no_semi.o(.text)
    .text                                    0x080015be   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x080015be   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080015fc   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001642   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x080016a2   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x080019da   Section      198  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001aa0   Section       40  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001ac8   Section       40  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001af0   Section      580  btod.o(CL$$btod_mult_common)
    i.ADC_IRQHandler                         0x08001d34   Section        0  stm32f4xx_it.o(i.ADC_IRQHandler)
    i.ADC_Init                               0x08001d40   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x08001d41   Thumb Code   298  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.BusFault_Handler                       0x08001e70   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.CAN1_Mode_Init                         0x08001e74   Section        0  can.o(i.CAN1_Mode_Init)
    i.CAN1_RX0_IRQHandler                    0x08001ed0   Section        0  stm32f4xx_it.o(i.CAN1_RX0_IRQHandler)
    i.CAN1_RX1_IRQHandler                    0x08001edc   Section        0  stm32f4xx_it.o(i.CAN1_RX1_IRQHandler)
    i.CAN1_SCE_IRQHandler                    0x08001ee8   Section        0  stm32f4xx_it.o(i.CAN1_SCE_IRQHandler)
    i.CAN1_TX_IRQHandler                     0x08001ef4   Section        0  stm32f4xx_it.o(i.CAN1_TX_IRQHandler)
    i.CAN_Config                             0x08001f00   Section        0  can.o(i.CAN_Config)
    i.CAN_senddata                           0x08001f78   Section        0  can.o(i.CAN_senddata)
    i.DH11_ReadDate_Handle                   0x08001fc8   Section        0  dht11.o(i.DH11_ReadDate_Handle)
    i.DHT11_Check                            0x0800202c   Section        0  dht11.o(i.DHT11_Check)
    i.DHT11_IO_IN                            0x08002084   Section        0  dht11.o(i.DHT11_IO_IN)
    i.DHT11_IO_OUT                           0x080020a0   Section        0  dht11.o(i.DHT11_IO_OUT)
    i.DHT11_ReadBit                          0x080020c0   Section        0  dht11.o(i.DHT11_ReadBit)
    i.DHT11_ReadByte                         0x08002118   Section        0  dht11.o(i.DHT11_ReadByte)
    i.DHT11_SendOut                          0x08002134   Section        0  dht11.o(i.DHT11_SendOut)
    i.DebugMon_Handler                       0x08002164   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.EXTI4_IRQHandler                       0x08002166   Section        0  stm32f4xx_it.o(i.EXTI4_IRQHandler)
    i.Error_Handler                          0x0800216c   Section        0  main.o(i.Error_Handler)
    i.HAL_ADCEx_InjectedConvCpltCallback     0x08002170   Section        0  stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    i.HAL_ADC_ConfigChannel                  0x08002174   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x080022cc   Section        0  main.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x08002388   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_GetValue                       0x0800238a   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue)
    i.HAL_ADC_IRQHandler                     0x08002390   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    i.HAL_ADC_Init                           0x080024c2   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_LevelOutOfWindowCallback       0x08002516   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    i.HAL_ADC_MspInit                        0x08002518   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_IT                       0x08002588   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT)
    i.HAL_CAN_ActivateNotification           0x080026ac   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_ActivateNotification)
    i.HAL_CAN_AddTxMessage                   0x080026d0   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_AddTxMessage)
    i.HAL_CAN_ConfigFilter                   0x080027b4   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_ConfigFilter)
    i.HAL_CAN_ErrorCallback                  0x080028c4   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_ErrorCallback)
    i.HAL_CAN_GetRxMessage                   0x080028c6   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_GetRxMessage)
    i.HAL_CAN_IRQHandler                     0x080029d8   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler)
    i.HAL_CAN_Init                           0x08002bd4   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_Init)
    i.HAL_CAN_MspInit                        0x08002d28   Section        0  can.o(i.HAL_CAN_MspInit)
    i.HAL_CAN_RxFifo0FullCallback            0x08002dd0   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback)
    i.HAL_CAN_RxFifo0MsgPendingCallback      0x08002dd4   Section        0  can.o(i.HAL_CAN_RxFifo0MsgPendingCallback)
    i.HAL_CAN_RxFifo1FullCallback            0x08002dfc   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback)
    i.HAL_CAN_RxFifo1MsgPendingCallback      0x08002dfe   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback)
    i.HAL_CAN_SleepCallback                  0x08002e00   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_SleepCallback)
    i.HAL_CAN_Start                          0x08002e02   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_Start)
    i.HAL_CAN_TxMailbox0AbortCallback        0x08002e5a   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback)
    i.HAL_CAN_TxMailbox0CompleteCallback     0x08002e5c   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback)
    i.HAL_CAN_TxMailbox1AbortCallback        0x08002e5e   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback)
    i.HAL_CAN_TxMailbox1CompleteCallback     0x08002e60   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback)
    i.HAL_CAN_TxMailbox2AbortCallback        0x08002e62   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback)
    i.HAL_CAN_TxMailbox2CompleteCallback     0x08002e64   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback)
    i.HAL_CAN_WakeUpFromRxMsgCallback        0x08002e66   Section        0  stm32f4xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback)
    i.HAL_DMA_Abort                          0x08002e68   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08002ef6   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_Delay                              0x08002f1c   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_EXTI_Callback                 0x08002f40   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x08002f44   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x08002f5c   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x0800312c   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x0800313a   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08003148   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08003154   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08003164   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08003198   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080031dc   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x0800320c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08003228   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08003268   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x0800328c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x080033d8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x080033e4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08003404   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08003424   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x0800348c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SPI_Init                           0x0800384c   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x0800390c   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_SPI_Transmit                       0x08003978   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit)
    i.HAL_SYSTICK_Config                     0x08003b0a   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x08003b32   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08003b34   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08003b36   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08003b8c   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08003c1c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08003c68   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start                     0x08003d08   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    i.HAL_TIM_Base_Start_IT                  0x08003d88   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_Base_Stop                      0x08003e14   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop)
    i.HAL_TIM_ConfigClockSource              0x08003e3a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x08003f22   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08003fd8   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start_IT               0x08004080   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT)
    i.HAL_TIM_IC_CaptureCallback             0x08004150   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08004152   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x0800428c   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x080042e0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x080042e2   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x080043ba   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08004404   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08004406   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08004408   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x080044e4   Section        0  main.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08004598   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_RxEventCallback             0x0800459a   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x0800459c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080045a0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080047d4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x0800483c   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08004940   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x0800495c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x0800495e   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08004a0c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08004a0e   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.KEY_Scan                               0x08004a10   Section        0  key.o(i.KEY_Scan)
    i.LCDDisplayData                         0x08004a84   Section        0  lcd.o(i.LCDDisplayData)
    i.LCD_Address_Set                        0x08004c14   Section        0  lcd_init.o(i.LCD_Address_Set)
    i.LCD_DrawPoint                          0x08004c56   Section        0  lcd.o(i.LCD_DrawPoint)
    i.LCD_Fill                               0x08004c6c   Section        0  lcd.o(i.LCD_Fill)
    i.LCD_Init                               0x08004cac   Section        0  lcd_init.o(i.LCD_Init)
    i.LCD_ShowChar                           0x08004e88   Section        0  lcd.o(i.LCD_ShowChar)
    i.LCD_ShowChinese                        0x08004f50   Section        0  lcd.o(i.LCD_ShowChinese)
    i.LCD_ShowChinese16x16                   0x08004fc0   Section        0  lcd.o(i.LCD_ShowChinese16x16)
    i.LCD_ShowChinese24x24                   0x0800509c   Section        0  lcd.o(i.LCD_ShowChinese24x24)
    i.LCD_ShowChinese32x32                   0x0800517c   Section        0  lcd.o(i.LCD_ShowChinese32x32)
    i.LCD_ShowFloatNum1                      0x08005258   Section        0  lcd.o(i.LCD_ShowFloatNum1)
    i.LCD_ShowIntNum                         0x080052f4   Section        0  lcd.o(i.LCD_ShowIntNum)
    i.LCD_ShowString                         0x08005378   Section        0  lcd.o(i.LCD_ShowString)
    i.LCD_WR_DATA                            0x080053ae   Section        0  lcd_init.o(i.LCD_WR_DATA)
    i.LCD_WR_DATA8                           0x080053c2   Section        0  lcd_init.o(i.LCD_WR_DATA8)
    i.LCD_WR_REG                             0x080053c8   Section        0  lcd_init.o(i.LCD_WR_REG)
    i.LCD_Writ_Bus                           0x080053f0   Section        0  lcd_init.o(i.LCD_Writ_Bus)
    i.MX_ADC1_Init                           0x08005424   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_CAN1_Init                           0x080054a0   Section        0  can.o(i.MX_CAN1_Init)
    i.MX_GPIO_Init                           0x080054e4   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_SPI2_Init                           0x08005664   Section        0  spi.o(i.MX_SPI2_Init)
    i.MX_TIM1_Init                           0x080056a8   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08005784   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x080057e8   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08005850   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_TIM6_Init                           0x080058b8   Section        0  tim.o(i.MX_TIM6_Init)
    i.MX_TIM7_Init                           0x08005900   Section        0  tim.o(i.MX_TIM7_Init)
    i.MX_USART1_UART_Init                    0x08005948   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08005980   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART6_UART_Init                    0x080059b8   Section        0  usart.o(i.MX_USART6_UART_Init)
    i.MemManage_Handler                      0x080059f0   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080059f2   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PID_Init                               0x080059f4   Section        0  pid.o(i.PID_Init)
    i.PendSV_Handler                         0x08005a24   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SPI_EndRxTxTransaction                 0x08005a28   Section        0  stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    SPI_EndRxTxTransaction                   0x08005a29   Thumb Code   114  stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    i.SPI_WaitFlagStateUntilTimeout          0x08005aa4   Section        0  stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    SPI_WaitFlagStateUntilTimeout            0x08005aa5   Thumb Code   184  stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    i.SVC_Handler                            0x08005b60   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08005b62   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08005b68   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08005bfc   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM6_DAC_IRQHandler                    0x08005c0c   Section        0  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    i.TIM7_IRQHandler                        0x08005c18   Section        0  stm32f4xx_it.o(i.TIM7_IRQHandler)
    i.TIM_Base_SetConfig                     0x08005c24   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08005d00   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x08005d1a   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08005d2e   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08005d2f   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x08005d40   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08005d41   Thumb Code    96  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08005da8   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08005e1c   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08005e1d   Thumb Code   104  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08005e8c   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08005e8d   Thumb Code    74  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08005ee0   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08005ee1   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08005f02   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08005f03   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART_DMAAbortOnError                   0x08005f26   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08005f27   Thumb Code    16  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08005f36   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08005f37   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08005f84   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08005f85   Thumb Code    26  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_Receive_IT                        0x08005f9e   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08005f9f   Thumb Code   200  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08006068   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08006069   Thumb Code   266  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x0800617c   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_Transmit_IT                       0x080061b4   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x080061b5   Thumb Code    94  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08006212   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08006213   Thumb Code   116  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART2_IRQHandler                      0x08006288   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.UsageFault_Handler                     0x08006294   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08006296   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x080062c6   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080062c7   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i._is_digit                              0x080062e6   Section        0  __printf_wp.o(i._is_digit)
    i.delay_us                               0x080062f4   Section        0  dht11.o(i.delay_us)
    i.displayInfrared                        0x08006324   Section        0  infrared.o(i.displayInfrared)
    i.driverKeyTestV                         0x080063bc   Section        0  key.o(i.driverKeyTestV)
    i.main                                   0x08006434   Section        0  main.o(i.main)
    i.mypow                                  0x08006744   Section        0  lcd.o(i.mypow)
    i.usart_printf                           0x08006754   Section        0  user_debug.o(i.usart_printf)
    i.usart_printfESP8266                    0x08006790   Section        0  user_debug.o(i.usart_printfESP8266)
    locale$$code                             0x080067cc   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x080067f8   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$d2f                                0x08006824   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x08006824   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$ddiv                               0x08006888   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08006888   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800688f   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dmul                               0x08006b38   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08006b38   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08006c8c   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08006c8c   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08006d28   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08006d28   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x08006d34   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08006d34   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08006d8a   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08006d8a   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x08006e16   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08006e16   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x08006e20   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x08006e20   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x08006e2a   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08006e2a   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08006e2e   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x08006e2e   Number         0  printf2.o(x$fpl$printf2)
    .constdata                               0x08006e32   Section       16  system_stm32f4xx.o(.constdata)
    x$fpl$usenofp                            0x08006e32   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08006e42   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08006e4a   Section    10456  lcd.o(.constdata)
    .constdata                               0x08009722   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08009722   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08009734   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08009734   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x0800973c   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x0800973c   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x08009750   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x08009764   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x08009764   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x08009777   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x0800978c   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800978c   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x080097c8   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x08009840   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08009844   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800984c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08009858   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800985a   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800985b   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x0800985c   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x0800985c   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x08009860   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08009868   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0800996c   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       48  main.o(.data)
    .data                                    0x20000030   Section       24  can.o(.data)
    .data                                    0x20000048   Section        4  can.o(.data)
    .data                                    0x2000004c   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x20000058   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x2000005c   Section        4  key.o(.data)
    key_up                                   0x2000005d   Data           1  key.o(.data)
    .data                                    0x20000060   Section       20  user_debug.o(.data)
    len                                      0x20000060   Data           2  user_debug.o(.data)
    len                                      0x20000062   Data           2  user_debug.o(.data)
    len                                      0x20000064   Data           2  user_debug.o(.data)
    ap                                       0x20000068   Data           4  user_debug.o(.data)
    ap                                       0x2000006c   Data           4  user_debug.o(.data)
    ap                                       0x20000070   Data           4  user_debug.o(.data)
    .data                                    0x20000074   Section        8  dht11.o(.data)
    .bss                                     0x2000007c   Section       50  main.o(.bss)
    .bss                                     0x200000b0   Section       72  adc.o(.bss)
    .bss                                     0x200000f8   Section       92  can.o(.bss)
    .bss                                     0x20000154   Section       88  spi.o(.bss)
    .bss                                     0x200001ac   Section      432  tim.o(.bss)
    .bss                                     0x2000035c   Section      216  usart.o(.bss)
    .bss                                     0x20000434   Section      768  user_debug.o(.bss)
    tx_buf                                   0x20000434   Data         256  user_debug.o(.bss)
    tx_buf                                   0x20000534   Data         256  user_debug.o(.bss)
    tx_buf                                   0x20000634   Data         256  user_debug.o(.bss)
    .bss                                     0x20000734   Section       96  libspace.o(.bss)
    HEAP                                     0x20000798   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x20000798   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20000998   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20000998   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20000d98   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x080001fd   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000203   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000209   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800020f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000215   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800021b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000221   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800022b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000231   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000237   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800023d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000243   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000249   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800024f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000255   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800025b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x08000261   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x08000267   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000271   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000277   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x0800027d   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x08000283   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x08000289   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800028d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800028f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002b1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x080002b5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002b5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002b5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002c7   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002cd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080002d5   Thumb Code     8  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART1_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x080002f1   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08000315   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000315   Thumb Code   238  lludivv7m.o(.text)
    vsprintf                                 0x08000405   Thumb Code    32  vsprintf.o(.text)
    __2sprintf                               0x08000429   Thumb Code    38  __2sprintf.o(.text)
    _printf_int_dec                          0x08000455   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x080004cd   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __aeabi_memcpy4                          0x08000655   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000655   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000655   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x0800069d   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x080006b9   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080006b9   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080006b9   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080006bd   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000707   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000709   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800070b   Thumb Code     2  heapauxi.o(.text)
    _printf_pre_padding                      0x0800070d   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000739   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x0800075b   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x0800076d   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x0800077f   Thumb Code    82  _printf_str.o(.text)
    _printf_int_common                       0x080007d1   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_charcount                        0x08000883   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x080008b7   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x080008dd   Thumb Code    10  _sputc.o(.text)
    _printf_wctomb                           0x080008e9   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x080009a5   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000a21   Thumb Code    68  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000a65   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000a7d   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000a91   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000ae7   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000b03   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000b0f   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    _ll_udiv10                               0x08000b25   Thumb Code   138  lludiv10.o(.text)
    __lib_sel_fp_printf                      0x08000baf   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000d5d   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x08000fc9   Thumb Code   754  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x080012c5   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080012d9   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x080012e9   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x080012f1   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001305   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001315   Thumb Code     8  _printf_wchar.o(.text)
    _wcrtomb                                 0x0800131d   Thumb Code    64  _wcrtomb.o(.text)
    __user_setup_stackheap                   0x0800135d   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x080013a9   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x080013b9   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x080013c1   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08001441   Thumb Code   216  bigflt0.o(.text)
    exit                                     0x0800151d   Thumb Code    12  exit.o(.text)
    __user_libspace                          0x08001529   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001529   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001529   Thumb Code     0  libspace.o(.text)
    strcmp                                   0x08001531   Thumb Code   128  strcmpv7m.o(.text)
    _sys_exit                                0x080015b1   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x080015bd   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080015bd   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080015bf   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x080015bf   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080015fd   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001643   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x080016a3   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x080019db   Thumb Code   198  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001aa1   Thumb Code    40  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001ac9   Thumb Code    40  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001af1   Thumb Code   580  btod.o(CL$$btod_mult_common)
    ADC_IRQHandler                           0x08001d35   Thumb Code     6  stm32f4xx_it.o(i.ADC_IRQHandler)
    BusFault_Handler                         0x08001e71   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    CAN1_Mode_Init                           0x08001e75   Thumb Code    84  can.o(i.CAN1_Mode_Init)
    CAN1_RX0_IRQHandler                      0x08001ed1   Thumb Code     6  stm32f4xx_it.o(i.CAN1_RX0_IRQHandler)
    CAN1_RX1_IRQHandler                      0x08001edd   Thumb Code     6  stm32f4xx_it.o(i.CAN1_RX1_IRQHandler)
    CAN1_SCE_IRQHandler                      0x08001ee9   Thumb Code     6  stm32f4xx_it.o(i.CAN1_SCE_IRQHandler)
    CAN1_TX_IRQHandler                       0x08001ef5   Thumb Code     6  stm32f4xx_it.o(i.CAN1_TX_IRQHandler)
    CAN_Config                               0x08001f01   Thumb Code   116  can.o(i.CAN_Config)
    CAN_senddata                             0x08001f79   Thumb Code    68  can.o(i.CAN_senddata)
    DH11_ReadDate_Handle                     0x08001fc9   Thumb Code    96  dht11.o(i.DH11_ReadDate_Handle)
    DHT11_Check                              0x0800202d   Thumb Code    84  dht11.o(i.DHT11_Check)
    DHT11_IO_IN                              0x08002085   Thumb Code    24  dht11.o(i.DHT11_IO_IN)
    DHT11_IO_OUT                             0x080020a1   Thumb Code    28  dht11.o(i.DHT11_IO_OUT)
    DHT11_ReadBit                            0x080020c1   Thumb Code    84  dht11.o(i.DHT11_ReadBit)
    DHT11_ReadByte                           0x08002119   Thumb Code    26  dht11.o(i.DHT11_ReadByte)
    DHT11_SendOut                            0x08002135   Thumb Code    44  dht11.o(i.DHT11_SendOut)
    DebugMon_Handler                         0x08002165   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    EXTI4_IRQHandler                         0x08002167   Thumb Code     6  stm32f4xx_it.o(i.EXTI4_IRQHandler)
    Error_Handler                            0x0800216d   Thumb Code     4  main.o(i.Error_Handler)
    HAL_ADCEx_InjectedConvCpltCallback       0x08002171   Thumb Code     2  stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    HAL_ADC_ConfigChannel                    0x08002175   Thumb Code   326  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x080022cd   Thumb Code   156  main.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ErrorCallback                    0x08002389   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_GetValue                         0x0800238b   Thumb Code     6  stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue)
    HAL_ADC_IRQHandler                       0x08002391   Thumb Code   306  stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    HAL_ADC_Init                             0x080024c3   Thumb Code    84  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_LevelOutOfWindowCallback         0x08002517   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    HAL_ADC_MspInit                          0x08002519   Thumb Code   100  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_IT                         0x08002589   Thumb Code   262  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT)
    HAL_CAN_ActivateNotification             0x080026ad   Thumb Code    36  stm32f4xx_hal_can.o(i.HAL_CAN_ActivateNotification)
    HAL_CAN_AddTxMessage                     0x080026d1   Thumb Code   226  stm32f4xx_hal_can.o(i.HAL_CAN_AddTxMessage)
    HAL_CAN_ConfigFilter                     0x080027b5   Thumb Code   266  stm32f4xx_hal_can.o(i.HAL_CAN_ConfigFilter)
    HAL_CAN_ErrorCallback                    0x080028c5   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_ErrorCallback)
    HAL_CAN_GetRxMessage                     0x080028c7   Thumb Code   274  stm32f4xx_hal_can.o(i.HAL_CAN_GetRxMessage)
    HAL_CAN_IRQHandler                       0x080029d9   Thumb Code   508  stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler)
    HAL_CAN_Init                             0x08002bd5   Thumb Code   338  stm32f4xx_hal_can.o(i.HAL_CAN_Init)
    HAL_CAN_MspInit                          0x08002d29   Thumb Code   154  can.o(i.HAL_CAN_MspInit)
    HAL_CAN_RxFifo0FullCallback              0x08002dd1   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback)
    HAL_CAN_RxFifo0MsgPendingCallback        0x08002dd5   Thumb Code    28  can.o(i.HAL_CAN_RxFifo0MsgPendingCallback)
    HAL_CAN_RxFifo1FullCallback              0x08002dfd   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback)
    HAL_CAN_RxFifo1MsgPendingCallback        0x08002dff   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback)
    HAL_CAN_SleepCallback                    0x08002e01   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_SleepCallback)
    HAL_CAN_Start                            0x08002e03   Thumb Code    88  stm32f4xx_hal_can.o(i.HAL_CAN_Start)
    HAL_CAN_TxMailbox0AbortCallback          0x08002e5b   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback)
    HAL_CAN_TxMailbox0CompleteCallback       0x08002e5d   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback)
    HAL_CAN_TxMailbox1AbortCallback          0x08002e5f   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback)
    HAL_CAN_TxMailbox1CompleteCallback       0x08002e61   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback)
    HAL_CAN_TxMailbox2AbortCallback          0x08002e63   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback)
    HAL_CAN_TxMailbox2CompleteCallback       0x08002e65   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback)
    HAL_CAN_WakeUpFromRxMsgCallback          0x08002e67   Thumb Code     2  stm32f4xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback)
    HAL_DMA_Abort                            0x08002e69   Thumb Code   142  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08002ef7   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_Delay                                0x08002f1d   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_EXTI_Callback                   0x08002f41   Thumb Code     2  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x08002f45   Thumb Code    18  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x08002f5d   Thumb Code   420  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x0800312d   Thumb Code    14  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x0800313b   Thumb Code    14  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08003149   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08003155   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08003165   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08003199   Thumb Code    58  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080031dd   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x0800320d   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08003229   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08003269   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x0800328d   Thumb Code   310  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x080033d9   Thumb Code     6  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x080033e5   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08003405   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08003425   Thumb Code    94  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x0800348d   Thumb Code   942  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SPI_Init                             0x0800384d   Thumb Code   190  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x0800390d   Thumb Code    94  spi.o(i.HAL_SPI_MspInit)
    HAL_SPI_Transmit                         0x08003979   Thumb Code   402  stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit)
    HAL_SYSTICK_Config                       0x08003b0b   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08003b33   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08003b35   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x08003b37   Thumb Code    84  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08003b8d   Thumb Code   114  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08003c1d   Thumb Code    74  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003c69   Thumb Code   142  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start                       0x08003d09   Thumb Code   100  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    HAL_TIM_Base_Start_IT                    0x08003d89   Thumb Code   110  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_Base_Stop                        0x08003e15   Thumb Code    38  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop)
    HAL_TIM_ConfigClockSource                0x08003e3b   Thumb Code   232  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08003f23   Thumb Code   180  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08003fd9   Thumb Code   146  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start_IT                 0x08004081   Thumb Code   208  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT)
    HAL_TIM_IC_CaptureCallback               0x08004151   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08004153   Thumb Code   312  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x0800428d   Thumb Code    72  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x080042e1   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x080042e3   Thumb Code   216  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x080043bb   Thumb Code    74  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08004405   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08004407   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08004409   Thumb Code   190  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x080044e5   Thumb Code   140  main.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08004599   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_RxEventCallback               0x0800459b   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x0800459d   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080045a1   Thumb Code   558  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080047d5   Thumb Code   102  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x0800483d   Thumb Code   236  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08004941   Thumb Code    28  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x0800495d   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x0800495f   Thumb Code   174  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004a0d   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004a0f   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    KEY_Scan                                 0x08004a11   Thumb Code   106  key.o(i.KEY_Scan)
    LCDDisplayData                           0x08004a85   Thumb Code   338  lcd.o(i.LCDDisplayData)
    LCD_Address_Set                          0x08004c15   Thumb Code    66  lcd_init.o(i.LCD_Address_Set)
    LCD_DrawPoint                            0x08004c57   Thumb Code    22  lcd.o(i.LCD_DrawPoint)
    LCD_Fill                                 0x08004c6d   Thumb Code    64  lcd.o(i.LCD_Fill)
    LCD_Init                                 0x08004cad   Thumb Code   472  lcd_init.o(i.LCD_Init)
    LCD_ShowChar                             0x08004e89   Thumb Code   192  lcd.o(i.LCD_ShowChar)
    LCD_ShowChinese                          0x08004f51   Thumb Code   110  lcd.o(i.LCD_ShowChinese)
    LCD_ShowChinese16x16                     0x08004fc1   Thumb Code   214  lcd.o(i.LCD_ShowChinese16x16)
    LCD_ShowChinese24x24                     0x0800509d   Thumb Code   218  lcd.o(i.LCD_ShowChinese24x24)
    LCD_ShowChinese32x32                     0x0800517d   Thumb Code   214  lcd.o(i.LCD_ShowChinese32x32)
    LCD_ShowFloatNum1                        0x08005259   Thumb Code   152  lcd.o(i.LCD_ShowFloatNum1)
    LCD_ShowIntNum                           0x080052f5   Thumb Code   132  lcd.o(i.LCD_ShowIntNum)
    LCD_ShowString                           0x08005379   Thumb Code    54  lcd.o(i.LCD_ShowString)
    LCD_WR_DATA                              0x080053af   Thumb Code    20  lcd_init.o(i.LCD_WR_DATA)
    LCD_WR_DATA8                             0x080053c3   Thumb Code     4  lcd_init.o(i.LCD_WR_DATA8)
    LCD_WR_REG                               0x080053c9   Thumb Code    36  lcd_init.o(i.LCD_WR_REG)
    LCD_Writ_Bus                             0x080053f1   Thumb Code    44  lcd_init.o(i.LCD_Writ_Bus)
    MX_ADC1_Init                             0x08005425   Thumb Code   112  adc.o(i.MX_ADC1_Init)
    MX_CAN1_Init                             0x080054a1   Thumb Code    60  can.o(i.MX_CAN1_Init)
    MX_GPIO_Init                             0x080054e5   Thumb Code   360  gpio.o(i.MX_GPIO_Init)
    MX_SPI2_Init                             0x08005665   Thumb Code    60  spi.o(i.MX_SPI2_Init)
    MX_TIM1_Init                             0x080056a9   Thumb Code   212  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08005785   Thumb Code    96  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x080057e9   Thumb Code    96  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08005851   Thumb Code    96  tim.o(i.MX_TIM4_Init)
    MX_TIM6_Init                             0x080058b9   Thumb Code    62  tim.o(i.MX_TIM6_Init)
    MX_TIM7_Init                             0x08005901   Thumb Code    62  tim.o(i.MX_TIM7_Init)
    MX_USART1_UART_Init                      0x08005949   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08005981   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MX_USART6_UART_Init                      0x080059b9   Thumb Code    48  usart.o(i.MX_USART6_UART_Init)
    MemManage_Handler                        0x080059f1   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080059f3   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PID_Init                                 0x080059f5   Thumb Code    42  pid.o(i.PID_Init)
    PendSV_Handler                           0x08005a25   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08005b61   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08005b63   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08005b69   Thumb Code   140  main.o(i.SystemClock_Config)
    SystemInit                               0x08005bfd   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM6_DAC_IRQHandler                      0x08005c0d   Thumb Code     6  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    TIM7_IRQHandler                          0x08005c19   Thumb Code     6  stm32f4xx_it.o(i.TIM7_IRQHandler)
    TIM_Base_SetConfig                       0x08005c25   Thumb Code   174  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08005d01   Thumb Code    26  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x08005d1b   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08005da9   Thumb Code   106  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART_Start_Receive_IT                    0x0800617d   Thumb Code    56  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART2_IRQHandler                        0x08006289   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    UsageFault_Handler                       0x08006295   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08006297   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x080062e7   Thumb Code    14  __printf_wp.o(i._is_digit)
    delay_us                                 0x080062f5   Thumb Code    44  dht11.o(i.delay_us)
    displayInfrared                          0x08006325   Thumb Code   148  infrared.o(i.displayInfrared)
    driverKeyTestV                           0x080063bd   Thumb Code   116  key.o(i.driverKeyTestV)
    main                                     0x08006435   Thumb Code   646  main.o(i.main)
    mypow                                    0x08006745   Thumb Code    16  lcd.o(i.mypow)
    usart_printf                             0x08006755   Thumb Code    46  user_debug.o(i.usart_printf)
    usart_printfESP8266                      0x08006791   Thumb Code    46  user_debug.o(i.usart_printfESP8266)
    _get_lc_numeric                          0x080067cd   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x080067f9   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2f                              0x08006825   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08006825   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_ddiv                             0x08006889   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08006889   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_dmul                             0x08006b39   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08006b39   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08006c8d   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08006d29   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08006d35   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08006d35   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08006d8b   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x08006e17   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08006e1f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08006e1f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x08006e21   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x08006e2b   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08006e2f   Thumb Code     4  printf2.o(x$fpl$printf2)
    AHBPrescTable                            0x08006e32   Data          16  system_stm32f4xx.o(.constdata)
    __I$use$fp                               0x08006e32   Number         0  usenofp.o(x$fpl$usenofp)
    APBPrescTable                            0x08006e42   Data           8  system_stm32f4xx.o(.constdata)
    ascii_1608                               0x08006e4a   Data        1520  lcd.o(.constdata)
    ascii_3216                               0x0800743a   Data        6080  lcd.o(.constdata)
    tfont16                                  0x08008bfa   Data        1836  lcd.o(.constdata)
    tfont24                                  0x08009326   Data         370  lcd.o(.constdata)
    tfont32                                  0x08009498   Data         650  lcd.o(.constdata)
    Region$$Table$$Base                      0x08009820   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08009840   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x08009869   Data           0  lc_ctype_c.o(locale$$data)
    rx_data                                  0x20000000   Data           1  main.o(.data)
    a                                        0x20000002   Data           2  main.o(.data)
    b                                        0x20000004   Data           2  main.o(.data)
    speed1_pulse                             0x20000006   Data           2  main.o(.data)
    speed1                                   0x20000008   Data           2  main.o(.data)
    speed1_direction                         0x2000000a   Data           2  main.o(.data)
    speed2_pulse                             0x2000000c   Data           2  main.o(.data)
    speed2                                   0x2000000e   Data           2  main.o(.data)
    speed2_direction                         0x20000010   Data           2  main.o(.data)
    adc_v                                    0x20000014   Data           4  main.o(.data)
    power                                    0x20000018   Data           4  main.o(.data)
    adc_b                                    0x2000001c   Data           4  main.o(.data)
    count                                    0x20000020   Data           4  main.o(.data)
    abd                                      0x20000024   Data           4  main.o(.data)
    adc_v_1                                  0x20000028   Data           8  main.o(.data)
    pTxMailbox                               0x20000030   Data           4  can.o(.data)
    KEY                                      0x20000034   Data           4  can.o(.data)
    TXmessage                                0x20000038   Data           8  can.o(.data)
    RXmessage                                0x20000040   Data           8  can.o(.data)
    CAN_ID                                   0x20000048   Data           4  can.o(.data)
    uwTickFreq                               0x2000004c   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000050   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000054   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x20000058   Data           4  system_stm32f4xx.o(.data)
    countV                                   0x2000005c   Data           1  key.o(.data)
    pwm_val                                  0x2000005e   Data           2  key.o(.data)
    tem_integer                              0x20000074   Data           1  dht11.o(.data)
    tem_decimal                              0x20000075   Data           1  dht11.o(.data)
    hum_integer                              0x20000076   Data           1  dht11.o(.data)
    hum_decimal                              0x20000077   Data           1  dht11.o(.data)
    tem                                      0x20000078   Data           2  dht11.o(.data)
    hum                                      0x2000007a   Data           2  dht11.o(.data)
    str                                      0x2000007c   Data          50  main.o(.bss)
    hadc1                                    0x200000b0   Data          72  adc.o(.bss)
    TXHeader                                 0x200000f8   Data          24  can.o(.bss)
    RXHeader                                 0x20000110   Data          28  can.o(.bss)
    hcan1                                    0x2000012c   Data          40  can.o(.bss)
    hspi2                                    0x20000154   Data          88  spi.o(.bss)
    htim1                                    0x200001ac   Data          72  tim.o(.bss)
    htim2                                    0x200001f4   Data          72  tim.o(.bss)
    htim3                                    0x2000023c   Data          72  tim.o(.bss)
    htim4                                    0x20000284   Data          72  tim.o(.bss)
    htim6                                    0x200002cc   Data          72  tim.o(.bss)
    htim7                                    0x20000314   Data          72  tim.o(.bss)
    huart1                                   0x2000035c   Data          72  usart.o(.bss)
    huart2                                   0x200003a4   Data          72  usart.o(.bss)
    huart6                                   0x200003ec   Data          72  usart.o(.bss)
    __libspace_start                         0x20000734   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000794   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000099e8, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x0000996c, Max: 0x00080000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x00000008   Code   RO         4590  * !!!main             c_w.l(__main.o)
    0x08000190   0x00000034   Code   RO         4928    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x0000001a   Code   RO         4930    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x00000002   PAD
    0x080001e0   0x0000001c   Code   RO         4932    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x00000000   Code   RO         4583    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x00000006   Code   RO         4671    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000202   0x00000006   Code   RO         4673    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000208   0x00000006   Code   RO         4677    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800020e   0x00000006   Code   RO         4678    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000214   0x00000006   Code   RO         4679    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800021a   0x00000006   Code   RO         4680    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000220   0x0000000a   Code   RO         4685    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800022a   0x00000006   Code   RO         4675    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000230   0x00000006   Code   RO         4582    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000236   0x00000006   Code   RO         4676    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800023c   0x00000006   Code   RO         4674    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000242   0x00000006   Code   RO         4672    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000248   0x00000006   Code   RO         4682    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800024e   0x00000006   Code   RO         4683    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000254   0x00000006   Code   RO         4684    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800025a   0x00000006   Code   RO         4689    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x08000260   0x00000006   Code   RO         4690    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x08000266   0x0000000a   Code   RO         4686    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000270   0x00000006   Code   RO         4669    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000276   0x00000006   Code   RO         4670    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800027c   0x00000006   Code   RO         4687    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x08000282   0x00000006   Code   RO         4688    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x08000288   0x00000004   Code   RO         4681    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800028c   0x00000002   Code   RO         4790    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800028e   0x00000004   Code   RO         4811    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000292   0x00000000   Code   RO         4814    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000292   0x00000000   Code   RO         4817    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000292   0x00000000   Code   RO         4819    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000292   0x00000000   Code   RO         4821    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000292   0x00000006   Code   RO         4822    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000298   0x00000000   Code   RO         4824    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000298   0x0000000c   Code   RO         4825    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002a4   0x00000000   Code   RO         4826    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002a4   0x00000000   Code   RO         4828    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002a4   0x0000000a   Code   RO         4829    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002ae   0x00000000   Code   RO         4830    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002ae   0x00000000   Code   RO         4832    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002ae   0x00000000   Code   RO         4834    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002ae   0x00000000   Code   RO         4836    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002ae   0x00000000   Code   RO         4838    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002ae   0x00000000   Code   RO         4840    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002ae   0x00000000   Code   RO         4842    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002ae   0x00000000   Code   RO         4844    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002ae   0x00000000   Code   RO         4848    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002ae   0x00000000   Code   RO         4850    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002ae   0x00000000   Code   RO         4852    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002ae   0x00000000   Code   RO         4854    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002ae   0x00000002   Code   RO         4855    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002b0   0x00000002   Code   RO         4886    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002b2   0x00000000   Code   RO         4913    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x080002b2   0x00000000   Code   RO         4916    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080002b2   0x00000000   Code   RO         4919    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080002b2   0x00000000   Code   RO         4921    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x080002b2   0x00000000   Code   RO         4924    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080002b2   0x00000002   Code   RO         4925    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080002b4   0x00000000   Code   RO         4618    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002b4   0x00000000   Code   RO         4709    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002b4   0x00000006   Code   RO         4721    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002ba   0x00000000   Code   RO         4711    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002ba   0x00000004   Code   RO         4712    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002be   0x00000000   Code   RO         4714    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002be   0x00000008   Code   RO         4715    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002c6   0x00000002   Code   RO         4798    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002c8   0x00000000   Code   RO         4859    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002c8   0x00000004   Code   RO         4860    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002cc   0x00000006   Code   RO         4861    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002d2   0x00000002   PAD
    0x080002d4   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000314   0x000000ee   Code   RO         4550    .text               c_w.l(lludivv7m.o)
    0x08000402   0x00000002   PAD
    0x08000404   0x00000024   Code   RO         4552    .text               c_w.l(vsprintf.o)
    0x08000428   0x0000002c   Code   RO         4554    .text               c_w.l(__2sprintf.o)
    0x08000454   0x00000078   Code   RO         4560    .text               c_w.l(_printf_dec.o)
    0x080004cc   0x00000188   Code   RO         4579    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000654   0x00000064   Code   RO         4584    .text               c_w.l(rt_memcpy_w.o)
    0x080006b8   0x0000004e   Code   RO         4586    .text               c_w.l(rt_memclr_w.o)
    0x08000706   0x00000006   Code   RO         4588    .text               c_w.l(heapauxi.o)
    0x0800070c   0x0000004e   Code   RO         4623    .text               c_w.l(_printf_pad.o)
    0x0800075a   0x00000024   Code   RO         4625    .text               c_w.l(_printf_truncate.o)
    0x0800077e   0x00000052   Code   RO         4627    .text               c_w.l(_printf_str.o)
    0x080007d0   0x000000b2   Code   RO         4629    .text               c_w.l(_printf_intcommon.o)
    0x08000882   0x00000028   Code   RO         4631    .text               c_w.l(_printf_charcount.o)
    0x080008aa   0x00000002   PAD
    0x080008ac   0x00000030   Code   RO         4633    .text               c_w.l(_printf_char_common.o)
    0x080008dc   0x0000000a   Code   RO         4635    .text               c_w.l(_sputc.o)
    0x080008e6   0x00000002   PAD
    0x080008e8   0x000000bc   Code   RO         4637    .text               c_w.l(_printf_wctomb.o)
    0x080009a4   0x0000007c   Code   RO         4640    .text               c_w.l(_printf_longlong_dec.o)
    0x08000a20   0x00000070   Code   RO         4646    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000a90   0x00000094   Code   RO         4666    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000b24   0x0000008a   Code   RO         4725    .text               c_w.l(lludiv10.o)
    0x08000bae   0x0000041a   Code   RO         4727    .text               c_w.l(_printf_fp_dec.o)
    0x08000fc8   0x000002fc   Code   RO         4729    .text               c_w.l(_printf_fp_hex.o)
    0x080012c4   0x0000002c   Code   RO         4734    .text               c_w.l(_printf_char.o)
    0x080012f0   0x0000002c   Code   RO         4736    .text               c_w.l(_printf_wchar.o)
    0x0800131c   0x00000040   Code   RO         4738    .text               c_w.l(_wcrtomb.o)
    0x0800135c   0x0000004a   Code   RO         4740    .text               c_w.l(sys_stackheap_outer.o)
    0x080013a6   0x00000002   PAD
    0x080013a8   0x00000010   Code   RO         4742    .text               c_w.l(rt_ctype_table.o)
    0x080013b8   0x00000008   Code   RO         4747    .text               c_w.l(rt_locale_intlibspace.o)
    0x080013c0   0x00000080   Code   RO         4749    .text               c_w.l(_printf_fp_infnan.o)
    0x08001440   0x000000dc   Code   RO         4751    .text               c_w.l(bigflt0.o)
    0x0800151c   0x0000000c   Code   RO         4779    .text               c_w.l(exit.o)
    0x08001528   0x00000008   Code   RO         4795    .text               c_w.l(libspace.o)
    0x08001530   0x00000080   Code   RO         4809    .text               c_w.l(strcmpv7m.o)
    0x080015b0   0x0000000c   Code   RO         4856    .text               c_w.l(sys_exit.o)
    0x080015bc   0x00000002   Code   RO         4875    .text               c_w.l(use_no_semi.o)
    0x080015be   0x00000000   Code   RO         4877    .text               c_w.l(indicate_semi.o)
    0x080015be   0x0000003e   Code   RO         4754    CL$$btod_d2e        c_w.l(btod.o)
    0x080015fc   0x00000046   Code   RO         4756    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001642   0x00000060   Code   RO         4755    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x080016a2   0x00000338   Code   RO         4764    CL$$btod_div_common  c_w.l(btod.o)
    0x080019da   0x000000c6   Code   RO         4761    CL$$btod_e2e        c_w.l(btod.o)
    0x08001aa0   0x00000028   Code   RO         4758    CL$$btod_ediv       c_w.l(btod.o)
    0x08001ac8   0x00000028   Code   RO         4757    CL$$btod_emul       c_w.l(btod.o)
    0x08001af0   0x00000244   Code   RO         4763    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001d34   0x0000000c   Code   RO          575    i.ADC_IRQHandler    stm32f4xx_it.o
    0x08001d40   0x00000130   Code   RO          757    i.ADC_Init          stm32f4xx_hal_adc.o
    0x08001e70   0x00000002   Code   RO          576    i.BusFault_Handler  stm32f4xx_it.o
    0x08001e72   0x00000002   PAD
    0x08001e74   0x0000005c   Code   RO          313    i.CAN1_Mode_Init    can.o
    0x08001ed0   0x0000000c   Code   RO          577    i.CAN1_RX0_IRQHandler  stm32f4xx_it.o
    0x08001edc   0x0000000c   Code   RO          578    i.CAN1_RX1_IRQHandler  stm32f4xx_it.o
    0x08001ee8   0x0000000c   Code   RO          579    i.CAN1_SCE_IRQHandler  stm32f4xx_it.o
    0x08001ef4   0x0000000c   Code   RO          580    i.CAN1_TX_IRQHandler  stm32f4xx_it.o
    0x08001f00   0x00000078   Code   RO          315    i.CAN_Config        can.o
    0x08001f78   0x00000050   Code   RO          316    i.CAN_senddata      can.o
    0x08001fc8   0x00000064   Code   RO         4478    i.DH11_ReadDate_Handle  dht11.o
    0x0800202c   0x00000058   Code   RO         4479    i.DHT11_Check       dht11.o
    0x08002084   0x0000001c   Code   RO         4480    i.DHT11_IO_IN       dht11.o
    0x080020a0   0x00000020   Code   RO         4481    i.DHT11_IO_OUT      dht11.o
    0x080020c0   0x00000058   Code   RO         4482    i.DHT11_ReadBit     dht11.o
    0x08002118   0x0000001a   Code   RO         4483    i.DHT11_ReadByte    dht11.o
    0x08002132   0x00000002   PAD
    0x08002134   0x00000030   Code   RO         4484    i.DHT11_SendOut     dht11.o
    0x08002164   0x00000002   Code   RO          581    i.DebugMon_Handler  stm32f4xx_it.o
    0x08002166   0x00000006   Code   RO          582    i.EXTI4_IRQHandler  stm32f4xx_it.o
    0x0800216c   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08002170   0x00000002   Code   RO          928    i.HAL_ADCEx_InjectedConvCpltCallback  stm32f4xx_hal_adc_ex.o
    0x08002172   0x00000002   PAD
    0x08002174   0x00000158   Code   RO          759    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x080022cc   0x000000bc   Code   RO           14    i.HAL_ADC_ConvCpltCallback  main.o
    0x08002388   0x00000002   Code   RO          763    i.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x0800238a   0x00000006   Code   RO          766    i.HAL_ADC_GetValue  stm32f4xx_hal_adc.o
    0x08002390   0x00000132   Code   RO          767    i.HAL_ADC_IRQHandler  stm32f4xx_hal_adc.o
    0x080024c2   0x00000054   Code   RO          768    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x08002516   0x00000002   Code   RO          769    i.HAL_ADC_LevelOutOfWindowCallback  stm32f4xx_hal_adc.o
    0x08002518   0x00000070   Code   RO          272    i.HAL_ADC_MspInit   adc.o
    0x08002588   0x00000124   Code   RO          776    i.HAL_ADC_Start_IT  stm32f4xx_hal_adc.o
    0x080026ac   0x00000024   Code   RO         2228    i.HAL_CAN_ActivateNotification  stm32f4xx_hal_can.o
    0x080026d0   0x000000e2   Code   RO         2229    i.HAL_CAN_AddTxMessage  stm32f4xx_hal_can.o
    0x080027b2   0x00000002   PAD
    0x080027b4   0x00000110   Code   RO         2230    i.HAL_CAN_ConfigFilter  stm32f4xx_hal_can.o
    0x080028c4   0x00000002   Code   RO         2233    i.HAL_CAN_ErrorCallback  stm32f4xx_hal_can.o
    0x080028c6   0x00000112   Code   RO         2236    i.HAL_CAN_GetRxMessage  stm32f4xx_hal_can.o
    0x080029d8   0x000001fc   Code   RO         2240    i.HAL_CAN_IRQHandler  stm32f4xx_hal_can.o
    0x08002bd4   0x00000152   Code   RO         2241    i.HAL_CAN_Init      stm32f4xx_hal_can.o
    0x08002d26   0x00000002   PAD
    0x08002d28   0x000000a8   Code   RO          318    i.HAL_CAN_MspInit   can.o
    0x08002dd0   0x00000002   Code   RO         2248    i.HAL_CAN_RxFifo0FullCallback  stm32f4xx_hal_can.o
    0x08002dd2   0x00000002   PAD
    0x08002dd4   0x00000028   Code   RO          319    i.HAL_CAN_RxFifo0MsgPendingCallback  can.o
    0x08002dfc   0x00000002   Code   RO         2250    i.HAL_CAN_RxFifo1FullCallback  stm32f4xx_hal_can.o
    0x08002dfe   0x00000002   Code   RO         2251    i.HAL_CAN_RxFifo1MsgPendingCallback  stm32f4xx_hal_can.o
    0x08002e00   0x00000002   Code   RO         2252    i.HAL_CAN_SleepCallback  stm32f4xx_hal_can.o
    0x08002e02   0x00000058   Code   RO         2253    i.HAL_CAN_Start     stm32f4xx_hal_can.o
    0x08002e5a   0x00000002   Code   RO         2255    i.HAL_CAN_TxMailbox0AbortCallback  stm32f4xx_hal_can.o
    0x08002e5c   0x00000002   Code   RO         2256    i.HAL_CAN_TxMailbox0CompleteCallback  stm32f4xx_hal_can.o
    0x08002e5e   0x00000002   Code   RO         2257    i.HAL_CAN_TxMailbox1AbortCallback  stm32f4xx_hal_can.o
    0x08002e60   0x00000002   Code   RO         2258    i.HAL_CAN_TxMailbox1CompleteCallback  stm32f4xx_hal_can.o
    0x08002e62   0x00000002   Code   RO         2259    i.HAL_CAN_TxMailbox2AbortCallback  stm32f4xx_hal_can.o
    0x08002e64   0x00000002   Code   RO         2260    i.HAL_CAN_TxMailbox2CompleteCallback  stm32f4xx_hal_can.o
    0x08002e66   0x00000002   Code   RO         2262    i.HAL_CAN_WakeUpFromRxMsgCallback  stm32f4xx_hal_can.o
    0x08002e68   0x0000008e   Code   RO         1539    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08002ef6   0x00000024   Code   RO         1540    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08002f1a   0x00000002   PAD
    0x08002f1c   0x00000024   Code   RO         1976    i.HAL_Delay         stm32f4xx_hal.o
    0x08002f40   0x00000002   Code   RO         1430    i.HAL_GPIO_EXTI_Callback  stm32f4xx_hal_gpio.o
    0x08002f42   0x00000002   PAD
    0x08002f44   0x00000018   Code   RO         1431    i.HAL_GPIO_EXTI_IRQHandler  stm32f4xx_hal_gpio.o
    0x08002f5c   0x000001d0   Code   RO         1432    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x0800312c   0x0000000e   Code   RO         1434    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x0800313a   0x0000000e   Code   RO         1436    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08003148   0x0000000c   Code   RO         1982    i.HAL_GetTick       stm32f4xx_hal.o
    0x08003154   0x00000010   Code   RO         1988    i.HAL_IncTick       stm32f4xx_hal.o
    0x08003164   0x00000034   Code   RO         1989    i.HAL_Init          stm32f4xx_hal.o
    0x08003198   0x00000044   Code   RO         1990    i.HAL_InitTick      stm32f4xx_hal.o
    0x080031dc   0x00000030   Code   RO          705    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x0800320c   0x0000001a   Code   RO         1824    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08003226   0x00000002   PAD
    0x08003228   0x00000040   Code   RO         1830    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08003268   0x00000024   Code   RO         1831    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x0800328c   0x0000014c   Code   RO         1033    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080033d8   0x0000000c   Code   RO         1038    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x080033e4   0x00000020   Code   RO         1040    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08003404   0x00000020   Code   RO         1041    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08003424   0x00000068   Code   RO         1042    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x0800348c   0x000003c0   Code   RO         1045    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x0800384c   0x000000be   Code   RO         2472    i.HAL_SPI_Init      stm32f4xx_hal_spi.o
    0x0800390a   0x00000002   PAD
    0x0800390c   0x0000006c   Code   RO          390    i.HAL_SPI_MspInit   spi.o
    0x08003978   0x00000192   Code   RO         2480    i.HAL_SPI_Transmit  stm32f4xx_hal_spi.o
    0x08003b0a   0x00000028   Code   RO         1835    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08003b32   0x00000002   Code   RO         3500    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x08003b34   0x00000002   Code   RO         3501    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x08003b36   0x00000054   Code   RO         3503    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08003b8a   0x00000002   PAD
    0x08003b8c   0x00000090   Code   RO         3519    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08003c1c   0x0000004a   Code   RO         2785    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08003c66   0x00000002   PAD
    0x08003c68   0x000000a0   Code   RO          432    i.HAL_TIM_Base_MspInit  tim.o
    0x08003d08   0x00000080   Code   RO         2788    i.HAL_TIM_Base_Start  stm32f4xx_hal_tim.o
    0x08003d88   0x0000008c   Code   RO         2790    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08003e14   0x00000026   Code   RO         2791    i.HAL_TIM_Base_Stop  stm32f4xx_hal_tim.o
    0x08003e3a   0x000000e8   Code   RO         2794    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08003f22   0x000000b4   Code   RO         2806    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08003fd6   0x00000002   PAD
    0x08003fd8   0x000000a8   Code   RO          434    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08004080   0x000000d0   Code   RO         2811    i.HAL_TIM_Encoder_Start_IT  stm32f4xx_hal_tim.o
    0x08004150   0x00000002   Code   RO         2819    i.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x08004152   0x00000138   Code   RO         2833    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x0800428a   0x00000002   PAD
    0x0800428c   0x00000054   Code   RO          435    i.HAL_TIM_MspPostInit  tim.o
    0x080042e0   0x00000002   Code   RO         2836    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x080042e2   0x000000d8   Code   RO         2857    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x080043ba   0x0000004a   Code   RO         2860    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08004404   0x00000002   Code   RO         2862    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08004406   0x00000002   Code   RO         2863    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08004408   0x000000dc   Code   RO         2865    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x080044e4   0x000000b4   Code   RO           15    i.HAL_TIM_PeriodElapsedCallback  main.o
    0x08004598   0x00000002   Code   RO         2876    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x0800459a   0x00000002   Code   RO         3779    i.HAL_UARTEx_RxEventCallback  stm32f4xx_hal_uart.o
    0x0800459c   0x00000002   Code   RO         3793    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x0800459e   0x00000002   PAD
    0x080045a0   0x00000234   Code   RO         3796    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080047d4   0x00000066   Code   RO         3797    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x0800483a   0x00000002   PAD
    0x0800483c   0x00000104   Code   RO          522    i.HAL_UART_MspInit  usart.o
    0x08004940   0x0000001c   Code   RO         3802    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x0800495c   0x00000002   Code   RO         3803    i.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x0800495e   0x000000ae   Code   RO         3805    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08004a0c   0x00000002   Code   RO         3808    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08004a0e   0x00000002   Code   RO          583    i.HardFault_Handler  stm32f4xx_it.o
    0x08004a10   0x00000074   Code   RO         4223    i.KEY_Scan          key.o
    0x08004a84   0x00000190   Code   RO         4260    i.LCDDisplayData    lcd.o
    0x08004c14   0x00000042   Code   RO         4379    i.LCD_Address_Set   lcd_init.o
    0x08004c56   0x00000016   Code   RO         4262    i.LCD_DrawPoint     lcd.o
    0x08004c6c   0x00000040   Code   RO         4264    i.LCD_Fill          lcd.o
    0x08004cac   0x000001dc   Code   RO         4380    i.LCD_Init          lcd_init.o
    0x08004e88   0x000000c8   Code   RO         4265    i.LCD_ShowChar      lcd.o
    0x08004f50   0x0000006e   Code   RO         4266    i.LCD_ShowChinese   lcd.o
    0x08004fbe   0x00000002   PAD
    0x08004fc0   0x000000dc   Code   RO         4267    i.LCD_ShowChinese16x16  lcd.o
    0x0800509c   0x000000e0   Code   RO         4268    i.LCD_ShowChinese24x24  lcd.o
    0x0800517c   0x000000dc   Code   RO         4269    i.LCD_ShowChinese32x32  lcd.o
    0x08005258   0x0000009c   Code   RO         4270    i.LCD_ShowFloatNum1  lcd.o
    0x080052f4   0x00000084   Code   RO         4271    i.LCD_ShowIntNum    lcd.o
    0x08005378   0x00000036   Code   RO         4273    i.LCD_ShowString    lcd.o
    0x080053ae   0x00000014   Code   RO         4381    i.LCD_WR_DATA       lcd_init.o
    0x080053c2   0x00000004   Code   RO         4382    i.LCD_WR_DATA8      lcd_init.o
    0x080053c6   0x00000002   PAD
    0x080053c8   0x00000028   Code   RO         4383    i.LCD_WR_REG        lcd_init.o
    0x080053f0   0x00000034   Code   RO         4384    i.LCD_Writ_Bus      lcd_init.o
    0x08005424   0x0000007c   Code   RO          273    i.MX_ADC1_Init      adc.o
    0x080054a0   0x00000044   Code   RO          320    i.MX_CAN1_Init      can.o
    0x080054e4   0x00000180   Code   RO          247    i.MX_GPIO_Init      gpio.o
    0x08005664   0x00000044   Code   RO          391    i.MX_SPI2_Init      spi.o
    0x080056a8   0x000000dc   Code   RO          436    i.MX_TIM1_Init      tim.o
    0x08005784   0x00000064   Code   RO          437    i.MX_TIM2_Init      tim.o
    0x080057e8   0x00000068   Code   RO          438    i.MX_TIM3_Init      tim.o
    0x08005850   0x00000068   Code   RO          439    i.MX_TIM4_Init      tim.o
    0x080058b8   0x00000048   Code   RO          440    i.MX_TIM6_Init      tim.o
    0x08005900   0x00000048   Code   RO          441    i.MX_TIM7_Init      tim.o
    0x08005948   0x00000038   Code   RO          523    i.MX_USART1_UART_Init  usart.o
    0x08005980   0x00000038   Code   RO          524    i.MX_USART2_UART_Init  usart.o
    0x080059b8   0x00000038   Code   RO          525    i.MX_USART6_UART_Init  usart.o
    0x080059f0   0x00000002   Code   RO          584    i.MemManage_Handler  stm32f4xx_it.o
    0x080059f2   0x00000002   Code   RO          585    i.NMI_Handler       stm32f4xx_it.o
    0x080059f4   0x00000030   Code   RO          727    i.PID_Init          pid.o
    0x08005a24   0x00000002   Code   RO          586    i.PendSV_Handler    stm32f4xx_it.o
    0x08005a26   0x00000002   PAD
    0x08005a28   0x0000007c   Code   RO         2510    i.SPI_EndRxTxTransaction  stm32f4xx_hal_spi.o
    0x08005aa4   0x000000bc   Code   RO         2515    i.SPI_WaitFlagStateUntilTimeout  stm32f4xx_hal_spi.o
    0x08005b60   0x00000002   Code   RO          587    i.SVC_Handler       stm32f4xx_it.o
    0x08005b62   0x00000004   Code   RO          588    i.SysTick_Handler   stm32f4xx_it.o
    0x08005b66   0x00000002   PAD
    0x08005b68   0x00000094   Code   RO           16    i.SystemClock_Config  main.o
    0x08005bfc   0x00000010   Code   RO         4141    i.SystemInit        system_stm32f4xx.o
    0x08005c0c   0x0000000c   Code   RO          589    i.TIM6_DAC_IRQHandler  stm32f4xx_it.o
    0x08005c18   0x0000000c   Code   RO          590    i.TIM7_IRQHandler   stm32f4xx_it.o
    0x08005c24   0x000000dc   Code   RO         2878    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08005d00   0x0000001a   Code   RO         2879    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x08005d1a   0x00000014   Code   RO         2889    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08005d2e   0x00000010   Code   RO         2890    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08005d3e   0x00000002   PAD
    0x08005d40   0x00000068   Code   RO         2891    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x08005da8   0x00000074   Code   RO         2892    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x08005e1c   0x00000070   Code   RO         2893    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x08005e8c   0x00000054   Code   RO         2894    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x08005ee0   0x00000022   Code   RO         2896    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005f02   0x00000024   Code   RO         2898    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005f26   0x00000010   Code   RO         3810    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08005f36   0x0000004e   Code   RO         3820    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08005f84   0x0000001a   Code   RO         3821    i.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x08005f9e   0x000000c8   Code   RO         3823    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08006066   0x00000002   PAD
    0x08006068   0x00000114   Code   RO         3824    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x0800617c   0x00000038   Code   RO         3826    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x080061b4   0x0000005e   Code   RO         3827    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08006212   0x00000074   Code   RO         3828    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08006286   0x00000002   PAD
    0x08006288   0x0000000c   Code   RO          591    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08006294   0x00000002   Code   RO          592    i.UsageFault_Handler  stm32f4xx_it.o
    0x08006296   0x00000030   Code   RO         4793    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x080062c6   0x00000020   Code   RO         1837    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080062e6   0x0000000e   Code   RO         4572    i._is_digit         c_w.l(__printf_wp.o)
    0x080062f4   0x00000030   Code   RO         4485    i.delay_us          dht11.o
    0x08006324   0x00000098   Code   RO         4179    i.displayInfrared   infrared.o
    0x080063bc   0x00000078   Code   RO         4224    i.driverKeyTestV    key.o
    0x08006434   0x00000310   Code   RO           18    i.main              main.o
    0x08006744   0x00000010   Code   RO         4274    i.mypow             lcd.o
    0x08006754   0x0000003c   Code   RO         4433    i.usart_printf      user_debug.o
    0x08006790   0x0000003c   Code   RO         4434    i.usart_printfESP8266  user_debug.o
    0x080067cc   0x0000002c   Code   RO         4777    locale$$code        c_w.l(lc_numeric_c.o)
    0x080067f8   0x0000002c   Code   RO         4803    locale$$code        c_w.l(lc_ctype_c.o)
    0x08006824   0x00000062   Code   RO         4592    x$fpl$d2f           fz_wm.l(d2f.o)
    0x08006886   0x00000002   PAD
    0x08006888   0x000002b0   Code   RO         4601    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08006b38   0x00000154   Code   RO         4614    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08006c8c   0x0000009c   Code   RO         4691    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08006d28   0x0000000c   Code   RO         4693    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08006d34   0x00000056   Code   RO         4616    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08006d8a   0x0000008c   Code   RO         4695    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x08006e16   0x0000000a   Code   RO         4871    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08006e20   0x0000000a   Code   RO         4697    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x08006e2a   0x00000004   Code   RO         4699    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08006e2e   0x00000004   Code   RO         4701    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08006e32   0x00000000   Code   RO         4707    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08006e32   0x00000010   Data   RO         4142    .constdata          system_stm32f4xx.o
    0x08006e42   0x00000008   Data   RO         4143    .constdata          system_stm32f4xx.o
    0x08006e4a   0x000028d8   Data   RO         4275    .constdata          lcd.o
    0x08009722   0x00000011   Data   RO         4580    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08009733   0x00000001   PAD
    0x08009734   0x00000008   Data   RO         4638    .constdata          c_w.l(_printf_wctomb.o)
    0x0800973c   0x00000028   Data   RO         4667    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x08009764   0x00000026   Data   RO         4730    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800978a   0x00000002   PAD
    0x0800978c   0x00000094   Data   RO         4752    .constdata          c_w.l(bigflt0.o)
    0x08009820   0x00000020   Data   RO         4926    Region$$Table       anon$$obj.o
    0x08009840   0x0000001c   Data   RO         4776    locale$$data        c_w.l(lc_numeric_c.o)
    0x0800985c   0x00000110   Data   RO         4802    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00000d98, Max: 0x0001c000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000030   Data   RW           26    .data               main.o
    0x20000030   0x00000018   Data   RW          322    .data               can.o
    0x20000048   0x00000004   Data   RW          323    .data               can.o
    0x2000004c   0x0000000c   Data   RW         1996    .data               stm32f4xx_hal.o
    0x20000058   0x00000004   Data   RW         4144    .data               system_stm32f4xx.o
    0x2000005c   0x00000004   Data   RW         4225    .data               key.o
    0x20000060   0x00000014   Data   RW         4437    .data               user_debug.o
    0x20000074   0x00000008   Data   RW         4486    .data               dht11.o
    0x2000007c   0x00000032   Zero   RW           19    .bss                main.o
    0x200000ae   0x00000002   PAD
    0x200000b0   0x00000048   Zero   RW          274    .bss                adc.o
    0x200000f8   0x0000005c   Zero   RW          321    .bss                can.o
    0x20000154   0x00000058   Zero   RW          392    .bss                spi.o
    0x200001ac   0x000001b0   Zero   RW          442    .bss                tim.o
    0x2000035c   0x000000d8   Zero   RW          526    .bss                usart.o
    0x20000434   0x00000300   Zero   RW         4436    .bss                user_debug.o
    0x20000734   0x00000060   Zero   RW         4796    .bss                c_w.l(libspace.o)
    0x20000794   0x00000004   PAD
    0x20000798   0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20000998   0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Base: 0x2001c000, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       236         24          0          0         72       1629   adc.o
       568         58          0         28         92       4478   can.o
       458         28          0          8          0       5060   dht11.o
       384         24          0          0          0       1107   gpio.o
       152          4          0          0          0       1246   infrared.o
       236         20          0          4          0       1469   key.o
      1818        100      10456          0          0      11488   lcd.o
       658         16          0          0          0       3522   lcd_init.o
      1304        218          0         48         50     733798   main.o
        48          6          0          0          0       1395   pid.o
       176         22          0          0         88       1601   spi.o
        64         26        392          0       1536        824   startup_stm32f407xx.o
       184         28          0         12          0       9229   stm32f4xx_hal.o
      1340         54          0          0          0       6285   stm32f4xx_hal_adc.o
         2          0          0          0          0        945   stm32f4xx_hal_adc_ex.o
      1766          6          0          0          0      13935   stm32f4xx_hal_can.o
       198         14          0          0          0      33783   stm32f4xx_hal_cortex.o
       178          0          0          0          0       1911   stm32f4xx_hal_dma.o
       518         50          0          0          0       4051   stm32f4xx_hal_gpio.o
        48          6          0          0          0        806   stm32f4xx_hal_msp.o
      1472         76          0          0          0       6168   stm32f4xx_hal_rcc.o
       904         14          0          0          0       4314   stm32f4xx_hal_spi.o
      2600        170          0          0          0      19903   stm32f4xx_hal_tim.o
       232         30          0          0          0       3253   stm32f4xx_hal_tim_ex.o
      1738         16          0          0          0      12638   stm32f4xx_hal_uart.o
       122         48          0          0          0       8013   stm32f4xx_it.o
        16          4         24          4          0       1067   system_stm32f4xx.o
      1084        100          0          0        432       6307   tim.o
       428         48          0          0        216       2996   usart.o
       120         28          0         20        768       2304   user_debug.o

    ----------------------------------------------------------------------
     19098       <USER>      <GROUP>        124       3256     905525   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        46          0          0          0          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        44          6          0          0          0         84   __2sprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1050          0          0          0          0        216   _printf_fp_dec.o
       764         10         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112          8          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       220          4        148          0          0         96   bigflt0.o
      1910        128          0          0          0        672   btod.o
        12          0          0          0          0         72   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        36          4          0          0          0         76   vsprintf.o
        98          4          0          0          0        140   d2f.o
       688        140          0          0          0        256   ddiv.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      8720        <USER>        <GROUP>          0        100       5884   Library Totals
        14          0          3          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7110        272        551          0         96       4228   c_w.l
      1548        168          0          0          0       1532   fz_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      8720        <USER>        <GROUP>          0        100       5884   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     27818       1678      11458        124       3356     890621   Grand Totals
     27818       1678      11458        124       3356     890621   ELF Image Totals
     27818       1678      11458        124          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                39276 (  38.36kB)
    Total RW  Size (RW Data + ZI Data)              3480 (   3.40kB)
    Total ROM Size (Code + RO Data + RW Data)      39400 (  38.48kB)

==============================================================================

